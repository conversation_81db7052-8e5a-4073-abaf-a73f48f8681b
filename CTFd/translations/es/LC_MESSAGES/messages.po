msgid ""
msgstr ""
"Project-Id-Version: CTFd\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-04-02 07:37-0400\n"
"PO-Revision-Date: 2025-09-24 00:30\n"
"Last-Translator: \n"
"Language-Team: Spanish\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: CTFd\n"
"X-Crowdin-Project-ID: 659064\n"
"X-Crowdin-Language: es-ES\n"
"X-Crowdin-File: /master/messages.pot\n"
"X-Crowdin-File-ID: 188\n"
"Language: es_ES\n"

#: CTFd/challenges.py:38
msgid "Challenge Visibility is set to Admins Only"
msgstr ""

#: CTFd/challenges.py:41 CTFd/utils/decorators/__init__.py:39
#, python-format
msgid "%(ctf_name)s has not started yet"
msgstr ""

#: CTFd/challenges.py:44
#, python-format
msgid "%(ctf_name)s is paused"
msgstr ""

#: CTFd/challenges.py:47 CTFd/utils/decorators/__init__.py:31
#, python-format
msgid "%(ctf_name)s has ended"
msgstr ""

#: CTFd/forms/auth.py:21 CTFd/forms/self.py:21
#: CTFd/themes/core-beta/templates/teams/private.html:329
#: CTFd/themes/core-beta/templates/teams/public.html:95
msgid "User Name"
msgstr "Nombre de usuario"

#: CTFd/forms/auth.py:23 CTFd/forms/auth.py:60 CTFd/forms/self.py:22
#: CTFd/forms/teams.py:230
msgid "Email"
msgstr "Correo electrónico"

#: CTFd/forms/auth.py:24 CTFd/forms/auth.py:50 CTFd/forms/auth.py:67
#: CTFd/forms/self.py:24 CTFd/forms/teams.py:231
msgid "Password"
msgstr "Contraseña"

#: CTFd/forms/auth.py:25 CTFd/forms/auth.py:51 CTFd/forms/auth.py:62
#: CTFd/forms/auth.py:69 CTFd/forms/self.py:29 CTFd/forms/teams.py:154
#: CTFd/forms/teams.py:237 CTFd/themes/core-beta/templates/challenge.html:147
msgid "Submit"
msgstr "Enviar"

#: CTFd/forms/auth.py:46
msgid "User Name or Email"
msgstr "Nombre de usuario o correo electrónico"

#: CTFd/forms/auth.py:55
msgid "Resend Confirmation Email"
msgstr "Reenviar correo de confirmación"

#: CTFd/forms/self.py:23 CTFd/forms/users.py:181
msgid "Language"
msgstr "Idioma"

#: CTFd/forms/self.py:25
msgid "Current Password"
msgstr "Contraseña actual"

#: CTFd/forms/self.py:26 CTFd/forms/teams.py:140 CTFd/forms/teams.py:218
#: CTFd/forms/teams.py:233 CTFd/forms/users.py:164
#: CTFd/themes/core-beta/templates/teams/teams.html:51
#: CTFd/themes/core-beta/templates/users/users.html:48
msgid "Affiliation"
msgstr "Afiliación"

#: CTFd/forms/self.py:27 CTFd/forms/teams.py:146 CTFd/forms/teams.py:219
#: CTFd/forms/teams.py:232 CTFd/forms/users.py:165
#: CTFd/themes/core-beta/templates/teams/teams.html:50
#: CTFd/themes/core-beta/templates/users/users.html:47
msgid "Website"
msgstr "Página web"

#: CTFd/forms/self.py:28 CTFd/forms/teams.py:150 CTFd/forms/teams.py:234
#: CTFd/themes/core-beta/templates/teams/teams.html:52
#: CTFd/themes/core-beta/templates/users/users.html:49
msgid "Country"
msgstr "País"

#: CTFd/forms/self.py:59 CTFd/themes/core-beta/templates/settings.html:225
msgid "Expiration"
msgstr "Caducidad"

#: CTFd/forms/self.py:61
msgid "Generate"
msgstr "Genere"

#: CTFd/forms/setup.py:29
msgid "Event Name"
msgstr "Nombre del evento"

#: CTFd/forms/setup.py:29
msgid "The name of your CTF event/workshop"
msgstr "El nombre de su evento/taller CTF"

#: CTFd/forms/setup.py:32
msgid "Event Description"
msgstr "Descripción del evento"

#: CTFd/forms/setup.py:32
msgid "Description for the CTF"
msgstr "Descripción de la FPC"

#: CTFd/forms/setup.py:35 CTFd/forms/setup.py:36
msgid "User Mode"
msgstr "Modo Usuario"

#: CTFd/forms/setup.py:36
msgid "Team Mode"
msgstr "Modo equipo"

#: CTFd/forms/setup.py:39
msgid "Controls whether users join together in teams to play (Team Mode) or play as themselves (User Mode)"
msgstr "Controla si los usuarios se unen en equipos para jugar (Modo Equipo) o juegan como ellos mismos (Modo Usuario)"

#: CTFd/forms/setup.py:45
msgid "Admin Username"
msgstr "Nombre de usuario"

#: CTFd/forms/setup.py:46
msgid "Your username for the administration account"
msgstr "Su nombre de usuario para la cuenta de administración"

#: CTFd/forms/setup.py:50
msgid "Admin Email"
msgstr "Correo electrónico del administrador"

#: CTFd/forms/setup.py:51
msgid "Your email address for the administration account"
msgstr "Su dirección de correo electrónico para la cuenta de administración"

#: CTFd/forms/setup.py:55
msgid "Admin Password"
msgstr "Contraseña de administrador"

#: CTFd/forms/setup.py:56
msgid "Your password for the administration account"
msgstr "Su contraseña para la cuenta de administración"

#: CTFd/forms/setup.py:61
msgid "Logo"
msgstr "Logotipo"

#: CTFd/forms/setup.py:63
msgid "Logo to use for the website instead of a CTF name. Used as the home page button. Optional."
msgstr "Logotipo para utilizar en el sitio web en lugar del nombre de la CTF. Se utiliza como botón de la página de inicio. Opcional."

#: CTFd/forms/setup.py:67
msgid "Banner"
msgstr "Banner"

#: CTFd/forms/setup.py:67
msgid "Banner to use for the homepage. Optional."
msgstr "Banner a utilizar para la página de inicio. Opcional."

#: CTFd/forms/setup.py:70
msgid "Small Icon"
msgstr "Icono pequeño"

#: CTFd/forms/setup.py:72
msgid "favicon used in user's browsers. Only PNGs accepted. Must be 32x32px. Optional."
msgstr "favicon utilizado en los navegadores de los usuarios. Sólo se aceptan PNG. Debe ser de 32x32px. Opcional."

#: CTFd/forms/setup.py:76
msgid "Theme"
msgstr "Tema"

#: CTFd/forms/setup.py:77
msgid "CTFd Theme to use. Can be changed later."
msgstr "CTFd Tema a utilizar. Se puede cambiar más tarde."

#: CTFd/forms/setup.py:84
msgid "Theme Color"
msgstr "Tema Color"

#: CTFd/forms/setup.py:86
msgid "Color used by theme to control aesthetics. Requires theme support. Optional."
msgstr "Color utilizado por el tema para controlar la estética. Requiere soporte de tema. Opcional."

#: CTFd/forms/setup.py:91
msgid "Verify Emails"
msgstr "Verificar correos electrónicos"

#: CTFd/forms/setup.py:143
msgid "Start Time"
msgstr "Hora de inicio"

#: CTFd/forms/setup.py:144
msgid "Time when your CTF is scheduled to start. Optional."
msgstr "Hora a la que está previsto que comience tu CTF. Opcional."

#: CTFd/forms/setup.py:147
msgid "End Time"
msgstr "Fin de los tiempos"

#: CTFd/forms/setup.py:148
msgid "Time when your CTF is scheduled to end. Optional."
msgstr "Hora a la que está previsto que finalice tu CTF. Opcional."

#: CTFd/forms/setup.py:150
msgid "Finish"
msgstr "Acabado"

#: CTFd/forms/teams.py:102 CTFd/forms/teams.py:109 CTFd/forms/teams.py:127
#: CTFd/forms/teams.py:229
msgid "Team Name"
msgstr "Nombre del equipo"

#: CTFd/forms/teams.py:103 CTFd/forms/teams.py:110
msgid "Team Password"
msgstr "Contraseña del equipo"

#: CTFd/forms/teams.py:104 CTFd/forms/teams.py:289
msgid "Join"
msgstr "Únete a"

#: CTFd/forms/teams.py:111
msgid "Create"
msgstr "Cree"

#: CTFd/forms/teams.py:128
msgid "Your team's public name shown to other competitors"
msgstr "El nombre público de tu equipo se muestra a otros competidores"

#: CTFd/forms/teams.py:131
msgid "New Team Password"
msgstr "Nueva contraseña del equipo"

#: CTFd/forms/teams.py:131
msgid "Set a new team join password"
msgstr "Establecer una nueva contraseña para unirse al equipo"

#: CTFd/forms/teams.py:134
msgid "Confirm Current Team Password"
msgstr "Confirmar la contraseña actual del equipo"

#: CTFd/forms/teams.py:136
msgid "Provide your current team password (or your password) to update your team's password"
msgstr "Proporcione la contraseña actual de su equipo (o su contraseña) para actualizar la contraseña de su equipo"

#: CTFd/forms/teams.py:142
msgid "Your team's affiliation publicly shown to other competitors"
msgstr "La afiliación de tu equipo se muestra públicamente a otros competidores"

#: CTFd/forms/teams.py:147
msgid "Your team's website publicly shown to other competitors"
msgstr "El sitio web de su equipo se muestra públicamente a otros competidores"

#: CTFd/forms/teams.py:152
msgid "Your team's country publicly shown to other competitors"
msgstr "El país de tu equipo se muestra públicamente a los demás competidores"

#: CTFd/forms/teams.py:192
msgid "Team Captain"
msgstr "Capitán del equipo"

#: CTFd/forms/teams.py:215 CTFd/forms/users.py:161
msgid "Search Field"
msgstr "Campo de búsqueda"

#: CTFd/forms/teams.py:217 CTFd/forms/users.py:163
#: CTFd/themes/core-beta/templates/challenge.html:206
msgid "Name"
msgstr "Nombre"

#: CTFd/forms/teams.py:224 CTFd/forms/users.py:171
msgid "Parameter"
msgstr "Parámetro"

#: CTFd/forms/teams.py:225 CTFd/forms/users.py:175
msgid "Search"
msgstr "Buscar en"

#: CTFd/forms/teams.py:235
msgid "Hidden"
msgstr "Oculto"

#: CTFd/forms/teams.py:236
msgid "Banned"
msgstr "Prohibido"

#: CTFd/forms/teams.py:285
msgid "Invite Link"
msgstr "Enlace de invitación"

#: CTFd/forms/users.py:133
msgid "Bracket"
msgstr ""

#: CTFd/forms/users.py:134
msgid "Competition bracket for your user"
msgstr ""

#: CTFd/forms/users.py:172
msgid "Search for matching users"
msgstr "Buscar usuarios coincidentes"

#: CTFd/themes/core-beta/templates/base.html:53
msgid "Powered by CTFd"
msgstr "Desarrollado por CTFd"

#: CTFd/themes/core-beta/templates/challenge.html:11
#: CTFd/themes/core-beta/templates/teams/private.html:388
#: CTFd/themes/core-beta/templates/teams/public.html:157
#: CTFd/themes/core-beta/templates/users/private.html:124
#: CTFd/themes/core-beta/templates/users/public.html:123
msgid "Challenge"
msgstr "Desafío"

#: CTFd/themes/core-beta/templates/challenge.html:19
#, python-format
msgid "%(num)d Solve"
msgid_plural "%(num)d Solves"
msgstr[0] "%(num)d Resolver"
msgstr[1] "%(num)d Resolver"

#: CTFd/themes/core-beta/templates/challenge.html:77
msgid "View Hint"
msgstr "Ver pista"

#: CTFd/themes/core-beta/templates/challenge.html:120
msgid "attempt"
msgid_plural "attempts"
msgstr[0] ""
msgstr[1] ""

#: CTFd/themes/core-beta/templates/challenge.html:135
msgid "Flag"
msgstr "Bandera"

#: CTFd/themes/core-beta/templates/challenge.html:169
msgid "Next Challenge"
msgstr "Próximo reto"

#: CTFd/themes/core-beta/templates/challenge.html:175
msgid "Share"
msgstr ""

#: CTFd/themes/core-beta/templates/challenge.html:207
#: CTFd/themes/core-beta/templates/setup.html:305
#: CTFd/themes/core-beta/templates/setup.html:326
msgid "Date"
msgstr "Fecha"

#: CTFd/themes/core-beta/templates/challenges.html:7
#: CTFd/themes/core-beta/templates/components/navbar.html:59
msgid "Challenges"
msgstr "Desafíos"

#: CTFd/themes/core-beta/templates/confirm.html:7
msgid "Confirm"
msgstr "Confirme"

#: CTFd/themes/core-beta/templates/confirm.html:18
msgid "We've sent a confirmation email to your email address."
msgstr "Hemos enviado un mensaje de confirmación a tu dirección de correo electrónico."

#: CTFd/themes/core-beta/templates/confirm.html:24
msgid "Please click the link in that email to confirm your account."
msgstr "Haga clic en el enlace de ese correo electrónico para confirmar su cuenta."

#: CTFd/themes/core-beta/templates/confirm.html:30
msgid "If the email doesn’t arrive, check your spam folder or contact an administrator to manually verify your account."
msgstr "Si el correo electrónico no llega, compruebe su carpeta de correo no deseado o póngase en contacto con un administrador para verificar manualmente su cuenta."

#: CTFd/themes/core-beta/templates/confirm.html:43
msgid "Change Email Address"
msgstr "Cambiar la dirección de correo electrónico"

#: CTFd/themes/core-beta/templates/components/navbar.html:164
#: CTFd/themes/core-beta/templates/components/navbar.html:169
#: CTFd/themes/core-beta/templates/login.html:7
msgid "Login"
msgstr "Inicio de sesión"

#: CTFd/themes/core-beta/templates/login.html:40
msgid "Forgot your password?"
msgstr "¿Ha olvidado su contraseña?"

#: CTFd/themes/core-beta/templates/components/navbar.html:85
#: CTFd/themes/core-beta/templates/components/navbar.html:91
#: CTFd/themes/core-beta/templates/notifications.html:7
msgid "Notifications"
msgstr "Notificaciones"

#: CTFd/themes/core-beta/templates/notifications.html:14
msgid "There are no notifications yet"
msgstr "Aún no hay notificaciones"

#: CTFd/themes/core-beta/templates/components/navbar.html:151
#: CTFd/themes/core-beta/templates/components/navbar.html:156
#: CTFd/themes/core-beta/templates/register.html:7
msgid "Register"
msgstr "Regístrese en"

#: CTFd/themes/core-beta/templates/register.html:35
msgid "Your username on the site"
msgstr "Su nombre de usuario en el sitio"

#: CTFd/themes/core-beta/templates/register.html:43
msgid "Never shown to the public"
msgstr "Nunca se muestra al público"

#: CTFd/themes/core-beta/templates/register.html:51
msgid "Password used to log into your account"
msgstr "Contraseña utilizada para acceder a su cuenta"

#: CTFd/themes/core-beta/templates/register.html:69
#, python-format
msgid "By registering, you agree to the <a href=\"%(privacy_link)s\" target=\"_blank\">privacy policy</a> and <a href=\"%(tos_link)s\" target=\"_blank\">terms of service</a>"
msgstr ""

#: CTFd/themes/core-beta/templates/reset_password.html:7
msgid "Reset Password"
msgstr "Restablecer contraseña"

#: CTFd/themes/core-beta/templates/reset_password.html:21
msgid "You can now reset the password for your account and log in. Please enter in a new password below."
msgstr "Ahora puede restablecer la contraseña de su cuenta e iniciar sesión. Introduzca una nueva contraseña a continuación."

#: CTFd/themes/core-beta/templates/reset_password.html:44
msgid "Please provide the email address associated with your account below."
msgstr "Indique a continuación la dirección de correo electrónico asociada a su cuenta."

#: CTFd/themes/core-beta/templates/components/navbar.html:52
#: CTFd/themes/core-beta/templates/scoreboard.html:7
msgid "Scoreboard"
msgstr "Marcador"

#: CTFd/themes/core-beta/templates/scoreboard.html:24
msgid "All"
msgstr ""

#: CTFd/themes/core-beta/templates/scoreboard.html:36
msgid "Place"
msgstr ""

#: CTFd/themes/core-beta/templates/scoreboard.html:38
#: CTFd/themes/core-beta/templates/teams/private.html:330
#: CTFd/themes/core-beta/templates/teams/public.html:96
msgid "Score"
msgstr "Puntuación"

#: CTFd/themes/core-beta/templates/scoreboard.html:60
msgid "Scoreboard is empty"
msgstr ""

#: CTFd/themes/core-beta/templates/components/navbar.html:124
#: CTFd/themes/core-beta/templates/components/navbar.html:129
#: CTFd/themes/core-beta/templates/settings.html:8
#: CTFd/themes/core-beta/templates/setup.html:50
msgid "Settings"
msgstr "Ajustes"

#: CTFd/themes/core-beta/templates/components/navbar.html:112
#: CTFd/themes/core-beta/templates/components/navbar.html:117
#: CTFd/themes/core-beta/templates/settings.html:21
msgid "Profile"
msgstr "Perfil"

#: CTFd/themes/core-beta/templates/settings.html:26
msgid "Access Tokens"
msgstr "Fichas de acceso"

#: CTFd/themes/core-beta/templates/settings.html:95
msgid "Your profile has been updated"
msgstr "Su perfil ha sido actualizado"

#: CTFd/themes/core-beta/templates/settings.html:103
#: CTFd/themes/core-beta/templates/teams/private.html:83
#: CTFd/themes/core-beta/templates/teams/private.html:198
msgid "Error:"
msgstr "Error:"

#: CTFd/themes/core-beta/templates/settings.html:129
msgid "API Key Generated"
msgstr "Clave API generada"

#: CTFd/themes/core-beta/templates/settings.html:137
msgid "Please copy your API Key, it won't be shown again!"
msgstr "Por favor, copie su clave API, ¡no se volverá a mostrar!"

#: CTFd/themes/core-beta/templates/settings.html:183
msgid "Active Tokens"
msgstr "Fichas activas"

#: CTFd/themes/core-beta/templates/settings.html:195
msgid "Delete Token"
msgstr "Borrar token"

#: CTFd/themes/core-beta/templates/settings.html:204
msgid "Are you sure you want to delete this token?"
msgstr "¿Estás seguro de que quieres borrar este token?"

#: CTFd/themes/core-beta/templates/settings.html:224
msgid "Created"
msgstr "Creado"

#: CTFd/themes/core-beta/templates/settings.html:226
msgid "Description"
msgstr "Descripción"

#: CTFd/themes/core-beta/templates/settings.html:227
msgid "Delete"
msgstr "Borrar"

#: CTFd/themes/core-beta/templates/setup.html:24
msgid "Setup"
msgstr "Configurar"

#: CTFd/themes/core-beta/templates/setup.html:44
msgid "General"
msgstr "General"

#: CTFd/themes/core-beta/templates/setup.html:47
msgid "Mode"
msgstr "Modo"

#: CTFd/themes/core-beta/templates/setup.html:53
msgid "Administration"
msgstr "Administración"

#: CTFd/themes/core-beta/templates/setup.html:56
msgid "Style"
msgstr "Estilo"

#: CTFd/themes/core-beta/templates/setup.html:59
msgid "Date &amp; Time"
msgstr "Fecha &amp; Hora"

#: CTFd/themes/core-beta/templates/setup.html:62
msgid "Integrations"
msgstr "Integraciones"

#: CTFd/themes/core-beta/templates/setup.html:86
#: CTFd/themes/core-beta/templates/setup.html:138
#: CTFd/themes/core-beta/templates/setup.html:202
#: CTFd/themes/core-beta/templates/setup.html:238
#: CTFd/themes/core-beta/templates/setup.html:296
#: CTFd/themes/core-beta/templates/setup.html:345
msgid "Next"
msgstr "Siguiente"

#: CTFd/themes/core-beta/templates/setup.html:112
msgid "Participants register accounts and form teams"
msgstr "Los participantes registran sus cuentas y forman equipos"

#: CTFd/themes/core-beta/templates/setup.html:113
msgid "If a team member solves a challenge, the entire team receives credit"
msgstr "Si un miembro del equipo resuelve un reto, todo el equipo recibe créditos"

#: CTFd/themes/core-beta/templates/setup.html:115
msgid "Easier to see which team member solved a challenge"
msgstr "Es más fácil ver qué miembro del equipo ha resuelto un reto"

#: CTFd/themes/core-beta/templates/setup.html:116
msgid "May be slightly more difficult to administer"
msgstr "Puede ser ligeramente más difícil de administrar"

#: CTFd/themes/core-beta/templates/setup.html:120
msgid "Participants only register an individual account"
msgstr "Los participantes sólo registran una cuenta individual"

#: CTFd/themes/core-beta/templates/setup.html:121
msgid "Players can share accounts to form pseudo-teams"
msgstr "Los jugadores pueden compartir cuentas para formar pseudoequipos"

#: CTFd/themes/core-beta/templates/setup.html:123
msgid "Easier to organize"
msgstr "Más fácil de organizar"

#: CTFd/themes/core-beta/templates/setup.html:124
msgid "Difficult to attribute solutions to individual team members"
msgstr "Dificultad para atribuir soluciones a miembros individuales del equipo"

#: CTFd/themes/core-beta/templates/setup.html:143
msgid "Visibility Settings"
msgstr "Ajustes de visibilidad"

#: CTFd/themes/core-beta/templates/setup.html:146
msgid "Control the visibility of different sections of CTFd"
msgstr "Controlar la visibilidad de las diferentes secciones de CTFd"

#: CTFd/themes/core-beta/templates/setup.html:232
msgid "Subscribe email address to the CTFd LLC Newsletter for news and updates"
msgstr "Suscriba su dirección de correo electrónico al boletín de CTFd LLC para recibir noticias y actualizaciones"

#: CTFd/themes/core-beta/templates/setup.html:309
#: CTFd/themes/core-beta/templates/setup.html:330
#: CTFd/themes/core-beta/templates/teams/private.html:391
#: CTFd/themes/core-beta/templates/teams/public.html:160
#: CTFd/themes/core-beta/templates/users/private.html:127
#: CTFd/themes/core-beta/templates/users/public.html:126
msgid "Time"
msgstr "Tiempo"

#: CTFd/themes/core-beta/templates/setup.html:334
msgid "UTC Preview"
msgstr "Avance de la UTC"

#: CTFd/themes/core-beta/templates/components/navbar.html:36
#: CTFd/themes/core-beta/templates/users/users.html:6
msgid "Users"
msgstr "Usuarios"

#: CTFd/themes/core-beta/templates/components/navbar.html:43
#: CTFd/themes/core-beta/templates/teams/teams.html:6
msgid "Teams"
msgstr "Equipos"

#: CTFd/themes/core-beta/templates/components/navbar.html:72
#: CTFd/themes/core-beta/templates/components/navbar.html:77
msgid "Admin Panel"
msgstr "Panel de administración"

#: CTFd/themes/core-beta/templates/components/navbar.html:99
#: CTFd/themes/core-beta/templates/components/navbar.html:104
#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:6
#: CTFd/themes/core-beta/templates/teams/teams.html:49
msgid "Team"
msgstr "Equipo"

#: CTFd/themes/core-beta/templates/components/navbar.html:136
#: CTFd/themes/core-beta/templates/components/navbar.html:141
msgid "Logout"
msgstr "Cierre de sesión"

#: CTFd/themes/core-beta/templates/components/navbar.html:180
msgid "Change language"
msgstr ""

#: CTFd/themes/core-beta/templates/components/navbar.html:199
#: CTFd/themes/core-beta/templates/components/navbar.html:204
msgid "Toggle theme"
msgstr ""

#: CTFd/themes/core-beta/templates/errors/403.html:12
msgid "403 Forbidden"
msgstr ""

#: CTFd/themes/core-beta/templates/errors/404.html:9
msgid "File not found"
msgstr "Archivo no encontrado"

#: CTFd/themes/core-beta/templates/errors/404.html:12
msgid "404 Not Found"
msgstr ""

#: CTFd/themes/core-beta/templates/errors/429.html:9
msgid "Too many requests"
msgstr "Demasiadas solicitudes"

#: CTFd/themes/core-beta/templates/errors/429.html:10
msgid "Please slow down!"
msgstr "¡Por favor, más despacio!"

#: CTFd/themes/core-beta/templates/errors/429.html:13
msgid "429 Too Many Requests"
msgstr ""

#: CTFd/themes/core-beta/templates/errors/502.html:12
msgid "502 Bad Gateway"
msgstr ""

#: CTFd/themes/core-beta/templates/macros/forms.html:13
#: CTFd/themes/core-beta/templates/macros/forms.html:36
#: CTFd/themes/core-beta/templates/macros/forms.html:65
msgid "(Optional)"
msgstr "(Opcional)"

#: CTFd/themes/core-beta/templates/teams/invite.html:6
#: CTFd/themes/core-beta/templates/teams/join_team.html:6
#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:36
msgid "Join Team"
msgstr "Únete al equipo"

#: CTFd/themes/core-beta/templates/teams/invite.html:15
#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:12
msgid "Welcome to"
msgstr "Bienvenido a"

#: CTFd/themes/core-beta/templates/teams/invite.html:19
msgid "Click the button below to join the team!"
msgstr "Haga clic en el botón de abajo para unirse al equipo!"

#: CTFd/themes/core-beta/templates/teams/new_team.html:6
#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:39
msgid "Create Team"
msgstr "Crear equipo"

#: CTFd/themes/core-beta/templates/teams/private.html:8
msgid "Edit Team"
msgstr "Equipo de edición"

#: CTFd/themes/core-beta/templates/teams/private.html:76
msgid "Your team's profile has been updated"
msgstr "El perfil de tu equipo ha sido actualizado"

#: CTFd/themes/core-beta/templates/teams/private.html:104
msgid "Choose Captain"
msgstr "Elige Capitán"

#: CTFd/themes/core-beta/templates/teams/private.html:127
msgid "Your captain rights have been transferred"
msgstr "Sus derechos de capitán han sido transferidos"

#: CTFd/themes/core-beta/templates/teams/private.html:152
msgid "Invite Users"
msgstr "Invitar a usuarios"

#: CTFd/themes/core-beta/templates/teams/private.html:171
msgid "Share this link with your team members for them to join your team"
msgstr "Comparte este enlace con los miembros de tu equipo para que se unan a él"

#: CTFd/themes/core-beta/templates/teams/private.html:174
msgid "Invite links can be re-used and expire after 1 day"
msgstr "Los enlaces de invitación pueden reutilizarse y caducan al cabo de 1 día"

#: CTFd/themes/core-beta/templates/teams/private.html:188
msgid "Disband Team"
msgstr "Disolver el equipo"

#: CTFd/themes/core-beta/templates/teams/private.html:193
msgid "Are you sure you want to disband your team?"
msgstr "¿Seguro que quieres disolver tu equipo?"

#: CTFd/themes/core-beta/templates/teams/private.html:206
msgid "No"
msgstr "No"

#: CTFd/themes/core-beta/templates/teams/private.html:207
msgid "Yes"
msgstr "Sí"

#: CTFd/themes/core-beta/templates/teams/private.html:218
#: CTFd/themes/core-beta/templates/teams/public.html:10
#: CTFd/themes/core-beta/templates/users/private.html:21
#: CTFd/themes/core-beta/templates/users/public.html:21
#: CTFd/themes/core-beta/templates/users/users.html:70
msgid "Official"
msgstr "Oficial"

#: CTFd/themes/core-beta/templates/teams/private.html:260
msgid "place"
msgstr "lugar"

#: CTFd/themes/core-beta/templates/teams/private.html:268
msgid "points"
msgstr "puntos"

#: CTFd/themes/core-beta/templates/teams/private.html:325
#: CTFd/themes/core-beta/templates/teams/public.html:91
msgid "Members"
msgstr "Miembros"

#: CTFd/themes/core-beta/templates/teams/private.html:345
#: CTFd/themes/core-beta/templates/teams/public.html:110
msgid "Captain"
msgstr "Capitán"

#: CTFd/themes/core-beta/templates/teams/private.html:361
#: CTFd/themes/core-beta/templates/teams/public.html:125
#: CTFd/themes/core-beta/templates/users/private.html:98
#: CTFd/themes/core-beta/templates/users/public.html:98
msgid "Awards"
msgstr "Premios"

#: CTFd/themes/core-beta/templates/teams/private.html:384
#: CTFd/themes/core-beta/templates/teams/public.html:153
#: CTFd/themes/core-beta/templates/users/private.html:120
msgid "Solves"
msgstr "Resuelve"

#: CTFd/themes/core-beta/templates/teams/private.html:389
#: CTFd/themes/core-beta/templates/teams/public.html:158
#: CTFd/themes/core-beta/templates/users/private.html:125
#: CTFd/themes/core-beta/templates/users/public.html:124
msgid "Category"
msgstr "Categoría"

#: CTFd/themes/core-beta/templates/teams/private.html:390
#: CTFd/themes/core-beta/templates/teams/public.html:159
#: CTFd/themes/core-beta/templates/users/private.html:126
#: CTFd/themes/core-beta/templates/users/public.html:125
msgid "Value"
msgstr "Valor"

#: CTFd/themes/core-beta/templates/teams/private.html:481
#: CTFd/themes/core-beta/templates/teams/public.html:248
#: CTFd/themes/core-beta/templates/users/private.html:213
#: CTFd/themes/core-beta/templates/users/public.html:214
msgid "No solves yet"
msgstr "Aún no hay soluciones"

#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:14
msgid "In order to participate you must either join or create a team."
msgstr "Para participar debes unirte o crear un equipo."

#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:22
msgid "Play with Official Team"
msgstr "Jugar con el equipo oficial"

#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:27
msgid "Join Unofficial Team"
msgstr "Únete al equipo no oficial"

#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:30
msgid "Create Unofficial Team"
msgstr "Crear equipo no oficial"

#: CTFd/themes/core-beta/templates/teams/teams.html:128
#: CTFd/themes/core-beta/templates/users/users.html:118
msgid "Page"
msgstr "Página"

#: CTFd/themes/core-beta/templates/users/users.html:14
#, python-format
msgid "Searching for users with <strong>%(field)s</strong> matching <strong>%(q)s</strong>"
msgstr ""

#: CTFd/themes/core-beta/templates/users/users.html:17
#, python-format
msgid "Page %(page)s of %(total)s results"
msgstr ""

#: CTFd/themes/core-beta/templates/users/users.html:46
msgid "User"
msgstr "Usuario"

#: CTFd/utils/modes/__init__.py:35
msgid "user"
msgid_plural "users"
msgstr[0] "usuario"
msgstr[1] "usuarios"

#: CTFd/utils/modes/__init__.py:37
msgid "team"
msgid_plural "teams"
msgstr[0] "equipo"
msgstr[1] "equipos"


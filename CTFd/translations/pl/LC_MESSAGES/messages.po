msgid ""
msgstr ""
"Project-Id-Version: CTFd\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-04-02 07:37-0400\n"
"PO-Revision-Date: 2025-09-24 00:30\n"
"Last-Translator: \n"
"Language-Team: Polish\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"
"X-Crowdin-Project: CTFd\n"
"X-Crowdin-Project-ID: 659064\n"
"X-Crowdin-Language: pl\n"
"X-Crowdin-File: /master/messages.pot\n"
"X-Crowdin-File-ID: 188\n"
"Language: pl_PL\n"

#: CTFd/challenges.py:38
msgid "Challenge Visibility is set to Admins Only"
msgstr ""

#: CTFd/challenges.py:41 CTFd/utils/decorators/__init__.py:39
#, python-format
msgid "%(ctf_name)s has not started yet"
msgstr ""

#: CTFd/challenges.py:44
#, python-format
msgid "%(ctf_name)s is paused"
msgstr ""

#: CTFd/challenges.py:47 CTFd/utils/decorators/__init__.py:31
#, python-format
msgid "%(ctf_name)s has ended"
msgstr ""

#: CTFd/forms/auth.py:21 CTFd/forms/self.py:21
#: CTFd/themes/core-beta/templates/teams/private.html:329
#: CTFd/themes/core-beta/templates/teams/public.html:95
msgid "User Name"
msgstr "Nazwa użytkownika"

#: CTFd/forms/auth.py:23 CTFd/forms/auth.py:60 CTFd/forms/self.py:22
#: CTFd/forms/teams.py:230
msgid "Email"
msgstr "Email"

#: CTFd/forms/auth.py:24 CTFd/forms/auth.py:50 CTFd/forms/auth.py:67
#: CTFd/forms/self.py:24 CTFd/forms/teams.py:231
msgid "Password"
msgstr "Hasło"

#: CTFd/forms/auth.py:25 CTFd/forms/auth.py:51 CTFd/forms/auth.py:62
#: CTFd/forms/auth.py:69 CTFd/forms/self.py:29 CTFd/forms/teams.py:154
#: CTFd/forms/teams.py:237 CTFd/themes/core-beta/templates/challenge.html:147
msgid "Submit"
msgstr "Wyślij"

#: CTFd/forms/auth.py:46
msgid "User Name or Email"
msgstr "Nazwa użytkownika lub adres email"

#: CTFd/forms/auth.py:55
msgid "Resend Confirmation Email"
msgstr "Ponownie wyślij email z potwierdzeniem"

#: CTFd/forms/self.py:23 CTFd/forms/users.py:181
msgid "Language"
msgstr "Język"

#: CTFd/forms/self.py:25
msgid "Current Password"
msgstr "Obecne hasło"

#: CTFd/forms/self.py:26 CTFd/forms/teams.py:140 CTFd/forms/teams.py:218
#: CTFd/forms/teams.py:233 CTFd/forms/users.py:164
#: CTFd/themes/core-beta/templates/teams/teams.html:51
#: CTFd/themes/core-beta/templates/users/users.html:48
msgid "Affiliation"
msgstr "Afiliacja"

#: CTFd/forms/self.py:27 CTFd/forms/teams.py:146 CTFd/forms/teams.py:219
#: CTFd/forms/teams.py:232 CTFd/forms/users.py:165
#: CTFd/themes/core-beta/templates/teams/teams.html:50
#: CTFd/themes/core-beta/templates/users/users.html:47
msgid "Website"
msgstr "Strona internetowa"

#: CTFd/forms/self.py:28 CTFd/forms/teams.py:150 CTFd/forms/teams.py:234
#: CTFd/themes/core-beta/templates/teams/teams.html:52
#: CTFd/themes/core-beta/templates/users/users.html:49
msgid "Country"
msgstr "Państwo"

#: CTFd/forms/self.py:59 CTFd/themes/core-beta/templates/settings.html:225
msgid "Expiration"
msgstr "Data wygaśnięcia"

#: CTFd/forms/self.py:61
msgid "Generate"
msgstr "Wygeneruj"

#: CTFd/forms/setup.py:29
msgid "Event Name"
msgstr "Nazwa wydarzenia"

#: CTFd/forms/setup.py:29
msgid "The name of your CTF event/workshop"
msgstr "Nazwa wydarzenia / warsztatów"

#: CTFd/forms/setup.py:32
msgid "Event Description"
msgstr "Opis wydarzenia"

#: CTFd/forms/setup.py:32
msgid "Description for the CTF"
msgstr "Opis CTFa"

#: CTFd/forms/setup.py:35 CTFd/forms/setup.py:36
msgid "User Mode"
msgstr "Tryb indywidualny"

#: CTFd/forms/setup.py:36
msgid "Team Mode"
msgstr "Tryb zespołowy"

#: CTFd/forms/setup.py:39
msgid "Controls whether users join together in teams to play (Team Mode) or play as themselves (User Mode)"
msgstr "Ustawienie odpowiadające za to, czy użytkownicy grają razem w zespołach (Tryb zespołowy) czy samodzielnie (Tryb indywidualny)"

#: CTFd/forms/setup.py:45
msgid "Admin Username"
msgstr "Nazwa administratora"

#: CTFd/forms/setup.py:46
msgid "Your username for the administration account"
msgstr "Twoja nazwa użytkownika dla konta administratora"

#: CTFd/forms/setup.py:50
msgid "Admin Email"
msgstr "Adres email administratora"

#: CTFd/forms/setup.py:51
msgid "Your email address for the administration account"
msgstr "Twój adres email dla konta administratora"

#: CTFd/forms/setup.py:55
msgid "Admin Password"
msgstr "Hasło administratora"

#: CTFd/forms/setup.py:56
msgid "Your password for the administration account"
msgstr "Twoje hasło dla konta administratora"

#: CTFd/forms/setup.py:61
msgid "Logo"
msgstr "Logo"

#: CTFd/forms/setup.py:63
msgid "Logo to use for the website instead of a CTF name. Used as the home page button. Optional."
msgstr "Logo do wyświetlania na stronie, zamiast nazwy CTFa. Funkcjonuje jako przycisk strony głównej. Opcjonalne."

#: CTFd/forms/setup.py:67
msgid "Banner"
msgstr "Baner"

#: CTFd/forms/setup.py:67
msgid "Banner to use for the homepage. Optional."
msgstr "Baner na stronę główną. Opcjonalny."

#: CTFd/forms/setup.py:70
msgid "Small Icon"
msgstr "Mała ikona"

#: CTFd/forms/setup.py:72
msgid "favicon used in user's browsers. Only PNGs accepted. Must be 32x32px. Optional."
msgstr "favicon używana w przeglądarce. Tylko pliki PNG. Musi mieć wymiary 32x32 piksele. Opcjonalna."

#: CTFd/forms/setup.py:76
msgid "Theme"
msgstr "Motyw"

#: CTFd/forms/setup.py:77
msgid "CTFd Theme to use. Can be changed later."
msgstr "Motyw CTFd. Może być zmieniony później."

#: CTFd/forms/setup.py:84
msgid "Theme Color"
msgstr "Kolor motywu"

#: CTFd/forms/setup.py:86
msgid "Color used by theme to control aesthetics. Requires theme support. Optional."
msgstr "Kolor używany przez motyw do zmiany estetyki. Wymaga wsparcia motywu. Opcjonalny."

#: CTFd/forms/setup.py:91
msgid "Verify Emails"
msgstr "Weryfikacja wiadomości e-mail"

#: CTFd/forms/setup.py:143
msgid "Start Time"
msgstr "Czas rozpoczęcia"

#: CTFd/forms/setup.py:144
msgid "Time when your CTF is scheduled to start. Optional."
msgstr "Czas rozpoczęcia CTFa. Opcjonalny."

#: CTFd/forms/setup.py:147
msgid "End Time"
msgstr "Czas zakończenia"

#: CTFd/forms/setup.py:148
msgid "Time when your CTF is scheduled to end. Optional."
msgstr "Czas zakończenia CTFa. Opcjonalny."

#: CTFd/forms/setup.py:150
msgid "Finish"
msgstr "Zakończ"

#: CTFd/forms/teams.py:102 CTFd/forms/teams.py:109 CTFd/forms/teams.py:127
#: CTFd/forms/teams.py:229
msgid "Team Name"
msgstr "Nazwa zespołu"

#: CTFd/forms/teams.py:103 CTFd/forms/teams.py:110
msgid "Team Password"
msgstr "Hasło zespołu"

#: CTFd/forms/teams.py:104 CTFd/forms/teams.py:289
msgid "Join"
msgstr "Dołącz"

#: CTFd/forms/teams.py:111
msgid "Create"
msgstr "Utwórz"

#: CTFd/forms/teams.py:128
msgid "Your team's public name shown to other competitors"
msgstr "Nazwa Twojego zespołu, wyświetlana innym uczestnikom"

#: CTFd/forms/teams.py:131
msgid "New Team Password"
msgstr "Nowe hasło zespołu"

#: CTFd/forms/teams.py:131
msgid "Set a new team join password"
msgstr "Ustaw nowe hasło zespołu"

#: CTFd/forms/teams.py:134
msgid "Confirm Current Team Password"
msgstr "Potwierdź aktualne hasło zespołu"

#: CTFd/forms/teams.py:136
msgid "Provide your current team password (or your password) to update your team's password"
msgstr "Podaj obecne hasło zespołu (lub swoje hasło) aby zmienić hasło zespołu"

#: CTFd/forms/teams.py:142
msgid "Your team's affiliation publicly shown to other competitors"
msgstr "Afiliacja Twojego zespołu, wyświetlana innym uczestnikom"

#: CTFd/forms/teams.py:147
msgid "Your team's website publicly shown to other competitors"
msgstr "Strona internetowa Twojego zespołu, wyświetlana innym uczestnikom"

#: CTFd/forms/teams.py:152
msgid "Your team's country publicly shown to other competitors"
msgstr "Państwo Twojego zespołu, wyświetlane innym uczestnikom"

#: CTFd/forms/teams.py:192
msgid "Team Captain"
msgstr "Kapitan zespołu"

#: CTFd/forms/teams.py:215 CTFd/forms/users.py:161
msgid "Search Field"
msgstr "Pole wyszukiwania"

#: CTFd/forms/teams.py:217 CTFd/forms/users.py:163
#: CTFd/themes/core-beta/templates/challenge.html:206
msgid "Name"
msgstr "Nazwa"

#: CTFd/forms/teams.py:224 CTFd/forms/users.py:171
msgid "Parameter"
msgstr "Parametr"

#: CTFd/forms/teams.py:225 CTFd/forms/users.py:175
msgid "Search"
msgstr "Szukaj"

#: CTFd/forms/teams.py:235
msgid "Hidden"
msgstr "Ukryty"

#: CTFd/forms/teams.py:236
msgid "Banned"
msgstr "Zablokowany"

#: CTFd/forms/teams.py:285
msgid "Invite Link"
msgstr "Link zaproszenia"

#: CTFd/forms/users.py:133
msgid "Bracket"
msgstr ""

#: CTFd/forms/users.py:134
msgid "Competition bracket for your user"
msgstr ""

#: CTFd/forms/users.py:172
msgid "Search for matching users"
msgstr "Szukaj użytkownika"

#: CTFd/themes/core-beta/templates/base.html:53
msgid "Powered by CTFd"
msgstr "Obsługiwane przez CTFd"

#: CTFd/themes/core-beta/templates/challenge.html:11
#: CTFd/themes/core-beta/templates/teams/private.html:388
#: CTFd/themes/core-beta/templates/teams/public.html:157
#: CTFd/themes/core-beta/templates/users/private.html:124
#: CTFd/themes/core-beta/templates/users/public.html:123
msgid "Challenge"
msgstr "Zadanie"

#: CTFd/themes/core-beta/templates/challenge.html:19
#, python-format
msgid "%(num)d Solve"
msgid_plural "%(num)d Solves"
msgstr[0] "%(num)d rozwiązanie"
msgstr[1] "%(num)d rozwiązania"
msgstr[2] "%(num)d rozwiązań"
msgstr[3] "%(num)d rozwiązania"

#: CTFd/themes/core-beta/templates/challenge.html:77
msgid "View Hint"
msgstr "Wyświetl podpowiedź"

#: CTFd/themes/core-beta/templates/challenge.html:120
msgid "attempt"
msgid_plural "attempts"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""

#: CTFd/themes/core-beta/templates/challenge.html:135
msgid "Flag"
msgstr "Flaga"

#: CTFd/themes/core-beta/templates/challenge.html:169
msgid "Next Challenge"
msgstr "Następne zadanie"

#: CTFd/themes/core-beta/templates/challenge.html:175
msgid "Share"
msgstr ""

#: CTFd/themes/core-beta/templates/challenge.html:207
#: CTFd/themes/core-beta/templates/setup.html:305
#: CTFd/themes/core-beta/templates/setup.html:326
msgid "Date"
msgstr "Data"

#: CTFd/themes/core-beta/templates/challenges.html:7
#: CTFd/themes/core-beta/templates/components/navbar.html:59
msgid "Challenges"
msgstr "Zadania"

#: CTFd/themes/core-beta/templates/confirm.html:7
msgid "Confirm"
msgstr "Potwierdź"

#: CTFd/themes/core-beta/templates/confirm.html:18
msgid "We've sent a confirmation email to your email address."
msgstr "Wysłaliśmy wiadomość email z linkiem potwierdzającym na Twój adres email."

#: CTFd/themes/core-beta/templates/confirm.html:24
msgid "Please click the link in that email to confirm your account."
msgstr "Kliknij link otrzymany w emailu, aby potwierdzić swoje konto."

#: CTFd/themes/core-beta/templates/confirm.html:30
msgid "If the email doesn’t arrive, check your spam folder or contact an administrator to manually verify your account."
msgstr "Jeśli wiadomość email nie dotrze, sprawdź folder SPAM lub skontaktuj się z administratorem, aby zweryfikować konto."

#: CTFd/themes/core-beta/templates/confirm.html:43
msgid "Change Email Address"
msgstr "Zmień adres email"

#: CTFd/themes/core-beta/templates/components/navbar.html:164
#: CTFd/themes/core-beta/templates/components/navbar.html:169
#: CTFd/themes/core-beta/templates/login.html:7
msgid "Login"
msgstr "Zaloguj się"

#: CTFd/themes/core-beta/templates/login.html:40
msgid "Forgot your password?"
msgstr "Zapomniałeś hasła?"

#: CTFd/themes/core-beta/templates/components/navbar.html:85
#: CTFd/themes/core-beta/templates/components/navbar.html:91
#: CTFd/themes/core-beta/templates/notifications.html:7
msgid "Notifications"
msgstr "Powiadomienia"

#: CTFd/themes/core-beta/templates/notifications.html:14
msgid "There are no notifications yet"
msgstr "Nie ma jeszcze żadnych powiadomień"

#: CTFd/themes/core-beta/templates/components/navbar.html:151
#: CTFd/themes/core-beta/templates/components/navbar.html:156
#: CTFd/themes/core-beta/templates/register.html:7
msgid "Register"
msgstr "Rejestracja"

#: CTFd/themes/core-beta/templates/register.html:35
msgid "Your username on the site"
msgstr "Twoja nazwa użytkownika na stronie"

#: CTFd/themes/core-beta/templates/register.html:43
msgid "Never shown to the public"
msgstr "Nigdy nie wyświetlany publicznie"

#: CTFd/themes/core-beta/templates/register.html:51
msgid "Password used to log into your account"
msgstr "Hasło używane do logowania się na Twoje konto"

#: CTFd/themes/core-beta/templates/register.html:69
#, python-format
msgid "By registering, you agree to the <a href=\"%(privacy_link)s\" target=\"_blank\">privacy policy</a> and <a href=\"%(tos_link)s\" target=\"_blank\">terms of service</a>"
msgstr ""

#: CTFd/themes/core-beta/templates/reset_password.html:7
msgid "Reset Password"
msgstr "Zresetuj hasło"

#: CTFd/themes/core-beta/templates/reset_password.html:21
msgid "You can now reset the password for your account and log in. Please enter in a new password below."
msgstr "Możesz teraz zresetować hasło do swojego konta i zalogować się. Wprowadź nowe hasło poniżej."

#: CTFd/themes/core-beta/templates/reset_password.html:44
msgid "Please provide the email address associated with your account below."
msgstr "Podaj poniżej adres email powiązany z Twoim kontem."

#: CTFd/themes/core-beta/templates/components/navbar.html:52
#: CTFd/themes/core-beta/templates/scoreboard.html:7
msgid "Scoreboard"
msgstr "Tablica wyników"

#: CTFd/themes/core-beta/templates/scoreboard.html:24
msgid "All"
msgstr ""

#: CTFd/themes/core-beta/templates/scoreboard.html:36
msgid "Place"
msgstr ""

#: CTFd/themes/core-beta/templates/scoreboard.html:38
#: CTFd/themes/core-beta/templates/teams/private.html:330
#: CTFd/themes/core-beta/templates/teams/public.html:96
msgid "Score"
msgstr "Wynik"

#: CTFd/themes/core-beta/templates/scoreboard.html:60
msgid "Scoreboard is empty"
msgstr ""

#: CTFd/themes/core-beta/templates/components/navbar.html:124
#: CTFd/themes/core-beta/templates/components/navbar.html:129
#: CTFd/themes/core-beta/templates/settings.html:8
#: CTFd/themes/core-beta/templates/setup.html:50
msgid "Settings"
msgstr "Ustawienia"

#: CTFd/themes/core-beta/templates/components/navbar.html:112
#: CTFd/themes/core-beta/templates/components/navbar.html:117
#: CTFd/themes/core-beta/templates/settings.html:21
msgid "Profile"
msgstr "Profil"

#: CTFd/themes/core-beta/templates/settings.html:26
msgid "Access Tokens"
msgstr "Tokeny dostępu"

#: CTFd/themes/core-beta/templates/settings.html:95
msgid "Your profile has been updated"
msgstr "Twój profil został zaktualizowany"

#: CTFd/themes/core-beta/templates/settings.html:103
#: CTFd/themes/core-beta/templates/teams/private.html:83
#: CTFd/themes/core-beta/templates/teams/private.html:198
msgid "Error:"
msgstr "Błąd:"

#: CTFd/themes/core-beta/templates/settings.html:129
msgid "API Key Generated"
msgstr "Klucz API został wygenerowany"

#: CTFd/themes/core-beta/templates/settings.html:137
msgid "Please copy your API Key, it won't be shown again!"
msgstr "Skopiuj swój klucz API, nie będzie on wyświetlony ponownie!"

#: CTFd/themes/core-beta/templates/settings.html:183
msgid "Active Tokens"
msgstr "Aktywne tokeny"

#: CTFd/themes/core-beta/templates/settings.html:195
msgid "Delete Token"
msgstr "Usuń token"

#: CTFd/themes/core-beta/templates/settings.html:204
msgid "Are you sure you want to delete this token?"
msgstr "Czy na pewno chcesz usunąć ten token?"

#: CTFd/themes/core-beta/templates/settings.html:224
msgid "Created"
msgstr "Data utworzenia"

#: CTFd/themes/core-beta/templates/settings.html:226
msgid "Description"
msgstr "Opis"

#: CTFd/themes/core-beta/templates/settings.html:227
msgid "Delete"
msgstr "Usuń"

#: CTFd/themes/core-beta/templates/setup.html:24
msgid "Setup"
msgstr "Konfiguracja"

#: CTFd/themes/core-beta/templates/setup.html:44
msgid "General"
msgstr "Ogólne"

#: CTFd/themes/core-beta/templates/setup.html:47
msgid "Mode"
msgstr "Tryb"

#: CTFd/themes/core-beta/templates/setup.html:53
msgid "Administration"
msgstr "Konto Administratora"

#: CTFd/themes/core-beta/templates/setup.html:56
msgid "Style"
msgstr "Wygląd"

#: CTFd/themes/core-beta/templates/setup.html:59
msgid "Date &amp; Time"
msgstr "Data &amp; Czas"

#: CTFd/themes/core-beta/templates/setup.html:62
msgid "Integrations"
msgstr "Integracje"

#: CTFd/themes/core-beta/templates/setup.html:86
#: CTFd/themes/core-beta/templates/setup.html:138
#: CTFd/themes/core-beta/templates/setup.html:202
#: CTFd/themes/core-beta/templates/setup.html:238
#: CTFd/themes/core-beta/templates/setup.html:296
#: CTFd/themes/core-beta/templates/setup.html:345
msgid "Next"
msgstr "Następny"

#: CTFd/themes/core-beta/templates/setup.html:112
msgid "Participants register accounts and form teams"
msgstr "Uczestnicy rejestrują indywidualne konta i tworzą zespoły"

#: CTFd/themes/core-beta/templates/setup.html:113
msgid "If a team member solves a challenge, the entire team receives credit"
msgstr "Jeśli członek zespołu rozwiąże zadanie, cały zespół otrzymuje punkty"

#: CTFd/themes/core-beta/templates/setup.html:115
msgid "Easier to see which team member solved a challenge"
msgstr "Łatwiej zobaczyć, który członek zespołu rozwiązał zadanie"

#: CTFd/themes/core-beta/templates/setup.html:116
msgid "May be slightly more difficult to administer"
msgstr "Może być nieco trudniejszy w administracji"

#: CTFd/themes/core-beta/templates/setup.html:120
msgid "Participants only register an individual account"
msgstr "Uczestnicy rejestrują się indywidualnie"

#: CTFd/themes/core-beta/templates/setup.html:121
msgid "Players can share accounts to form pseudo-teams"
msgstr "Uczestnicy mogą współdzielić konta, tworząc pseudo-zespoły"

#: CTFd/themes/core-beta/templates/setup.html:123
msgid "Easier to organize"
msgstr "Łatwiejszy w organizacji"

#: CTFd/themes/core-beta/templates/setup.html:124
msgid "Difficult to attribute solutions to individual team members"
msgstr "Trudniej przypisać rozwiązania konkretnym członkom zespołu"

#: CTFd/themes/core-beta/templates/setup.html:143
msgid "Visibility Settings"
msgstr "Ustawienia widoczności"

#: CTFd/themes/core-beta/templates/setup.html:146
msgid "Control the visibility of different sections of CTFd"
msgstr "Kontrola widoczności różnych sekcji CTFd"

#: CTFd/themes/core-beta/templates/setup.html:232
msgid "Subscribe email address to the CTFd LLC Newsletter for news and updates"
msgstr "Zasubskrybuj newsletter CTFd LLC, aby otrzymywać wiadomości i aktualizacje"

#: CTFd/themes/core-beta/templates/setup.html:309
#: CTFd/themes/core-beta/templates/setup.html:330
#: CTFd/themes/core-beta/templates/teams/private.html:391
#: CTFd/themes/core-beta/templates/teams/public.html:160
#: CTFd/themes/core-beta/templates/users/private.html:127
#: CTFd/themes/core-beta/templates/users/public.html:126
msgid "Time"
msgstr "Czas"

#: CTFd/themes/core-beta/templates/setup.html:334
msgid "UTC Preview"
msgstr "Podgląd czasu UTC"

#: CTFd/themes/core-beta/templates/components/navbar.html:36
#: CTFd/themes/core-beta/templates/users/users.html:6
msgid "Users"
msgstr "Użytkownicy"

#: CTFd/themes/core-beta/templates/components/navbar.html:43
#: CTFd/themes/core-beta/templates/teams/teams.html:6
msgid "Teams"
msgstr "Zespoły"

#: CTFd/themes/core-beta/templates/components/navbar.html:72
#: CTFd/themes/core-beta/templates/components/navbar.html:77
msgid "Admin Panel"
msgstr "Panel administratora"

#: CTFd/themes/core-beta/templates/components/navbar.html:99
#: CTFd/themes/core-beta/templates/components/navbar.html:104
#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:6
#: CTFd/themes/core-beta/templates/teams/teams.html:49
msgid "Team"
msgstr "Zespół"

#: CTFd/themes/core-beta/templates/components/navbar.html:136
#: CTFd/themes/core-beta/templates/components/navbar.html:141
msgid "Logout"
msgstr "Wyloguj"

#: CTFd/themes/core-beta/templates/components/navbar.html:180
msgid "Change language"
msgstr ""

#: CTFd/themes/core-beta/templates/components/navbar.html:199
#: CTFd/themes/core-beta/templates/components/navbar.html:204
msgid "Toggle theme"
msgstr ""

#: CTFd/themes/core-beta/templates/errors/403.html:12
msgid "403 Forbidden"
msgstr ""

#: CTFd/themes/core-beta/templates/errors/404.html:9
msgid "File not found"
msgstr "Strona nie istnieje"

#: CTFd/themes/core-beta/templates/errors/404.html:12
msgid "404 Not Found"
msgstr ""

#: CTFd/themes/core-beta/templates/errors/429.html:9
msgid "Too many requests"
msgstr "Za dużo zapytań do serwera"

#: CTFd/themes/core-beta/templates/errors/429.html:10
msgid "Please slow down!"
msgstr "Zwolnij proszę!"

#: CTFd/themes/core-beta/templates/errors/429.html:13
msgid "429 Too Many Requests"
msgstr ""

#: CTFd/themes/core-beta/templates/errors/502.html:12
msgid "502 Bad Gateway"
msgstr ""

#: CTFd/themes/core-beta/templates/macros/forms.html:13
#: CTFd/themes/core-beta/templates/macros/forms.html:36
#: CTFd/themes/core-beta/templates/macros/forms.html:65
msgid "(Optional)"
msgstr "(Opcjonalnie)"

#: CTFd/themes/core-beta/templates/teams/invite.html:6
#: CTFd/themes/core-beta/templates/teams/join_team.html:6
#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:36
msgid "Join Team"
msgstr "Dołącz do drużyny"

#: CTFd/themes/core-beta/templates/teams/invite.html:15
#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:12
msgid "Welcome to"
msgstr "Witamy na"

#: CTFd/themes/core-beta/templates/teams/invite.html:19
msgid "Click the button below to join the team!"
msgstr "Kliknij przycisk poniżej, aby dołączyć do zespołu!"

#: CTFd/themes/core-beta/templates/teams/new_team.html:6
#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:39
msgid "Create Team"
msgstr "Utwórz zespół"

#: CTFd/themes/core-beta/templates/teams/private.html:8
msgid "Edit Team"
msgstr "Edytuj zespół"

#: CTFd/themes/core-beta/templates/teams/private.html:76
msgid "Your team's profile has been updated"
msgstr "Profil zespołu został zaktualizowany"

#: CTFd/themes/core-beta/templates/teams/private.html:104
msgid "Choose Captain"
msgstr "Wybierz kapitana"

#: CTFd/themes/core-beta/templates/teams/private.html:127
msgid "Your captain rights have been transferred"
msgstr "Prawa kapitana zostały przeniesione"

#: CTFd/themes/core-beta/templates/teams/private.html:152
msgid "Invite Users"
msgstr "Zaproś użytkowników"

#: CTFd/themes/core-beta/templates/teams/private.html:171
msgid "Share this link with your team members for them to join your team"
msgstr "Udostępnij ten link członkom swojego zespołu, aby mogli do niego dołączyć"

#: CTFd/themes/core-beta/templates/teams/private.html:174
msgid "Invite links can be re-used and expire after 1 day"
msgstr "Linki do zaproszeń mogą być ponownie wykorzystane i wygasają po 1 dniu"

#: CTFd/themes/core-beta/templates/teams/private.html:188
msgid "Disband Team"
msgstr "Rozwiązanie zespołu"

#: CTFd/themes/core-beta/templates/teams/private.html:193
msgid "Are you sure you want to disband your team?"
msgstr "Czy na pewno chcesz rozwiązać swój zespół?"

#: CTFd/themes/core-beta/templates/teams/private.html:206
msgid "No"
msgstr "Nie"

#: CTFd/themes/core-beta/templates/teams/private.html:207
msgid "Yes"
msgstr "Tak"

#: CTFd/themes/core-beta/templates/teams/private.html:218
#: CTFd/themes/core-beta/templates/teams/public.html:10
#: CTFd/themes/core-beta/templates/users/private.html:21
#: CTFd/themes/core-beta/templates/users/public.html:21
#: CTFd/themes/core-beta/templates/users/users.html:70
msgid "Official"
msgstr "Oficjalne konto"

#: CTFd/themes/core-beta/templates/teams/private.html:260
msgid "place"
msgstr "miejsce"

#: CTFd/themes/core-beta/templates/teams/private.html:268
msgid "points"
msgstr "punkty"

#: CTFd/themes/core-beta/templates/teams/private.html:325
#: CTFd/themes/core-beta/templates/teams/public.html:91
msgid "Members"
msgstr "Członkowie"

#: CTFd/themes/core-beta/templates/teams/private.html:345
#: CTFd/themes/core-beta/templates/teams/public.html:110
msgid "Captain"
msgstr "Kapitan"

#: CTFd/themes/core-beta/templates/teams/private.html:361
#: CTFd/themes/core-beta/templates/teams/public.html:125
#: CTFd/themes/core-beta/templates/users/private.html:98
#: CTFd/themes/core-beta/templates/users/public.html:98
msgid "Awards"
msgstr "Nagrody"

#: CTFd/themes/core-beta/templates/teams/private.html:384
#: CTFd/themes/core-beta/templates/teams/public.html:153
#: CTFd/themes/core-beta/templates/users/private.html:120
msgid "Solves"
msgstr "Rozwiązania"

#: CTFd/themes/core-beta/templates/teams/private.html:389
#: CTFd/themes/core-beta/templates/teams/public.html:158
#: CTFd/themes/core-beta/templates/users/private.html:125
#: CTFd/themes/core-beta/templates/users/public.html:124
msgid "Category"
msgstr "Kategoria"

#: CTFd/themes/core-beta/templates/teams/private.html:390
#: CTFd/themes/core-beta/templates/teams/public.html:159
#: CTFd/themes/core-beta/templates/users/private.html:126
#: CTFd/themes/core-beta/templates/users/public.html:125
msgid "Value"
msgstr "Wartość"

#: CTFd/themes/core-beta/templates/teams/private.html:481
#: CTFd/themes/core-beta/templates/teams/public.html:248
#: CTFd/themes/core-beta/templates/users/private.html:213
#: CTFd/themes/core-beta/templates/users/public.html:214
msgid "No solves yet"
msgstr "Brak rozwiązań"

#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:14
msgid "In order to participate you must either join or create a team."
msgstr "Aby wziąć udział, musisz dołączyć do zespołu lub go utworzyć."

#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:22
msgid "Play with Official Team"
msgstr "Graj z oficjalną drużyną"

#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:27
msgid "Join Unofficial Team"
msgstr "Dołącz do nieoficjalnego zespołu"

#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:30
msgid "Create Unofficial Team"
msgstr "Utwórz nieoficjalny zespół"

#: CTFd/themes/core-beta/templates/teams/teams.html:128
#: CTFd/themes/core-beta/templates/users/users.html:118
msgid "Page"
msgstr "Strona"

#: CTFd/themes/core-beta/templates/users/users.html:14
#, python-format
msgid "Searching for users with <strong>%(field)s</strong> matching <strong>%(q)s</strong>"
msgstr ""

#: CTFd/themes/core-beta/templates/users/users.html:17
#, python-format
msgid "Page %(page)s of %(total)s results"
msgstr ""

#: CTFd/themes/core-beta/templates/users/users.html:46
msgid "User"
msgstr "Użytkownik"

#: CTFd/utils/modes/__init__.py:35
msgid "user"
msgid_plural "users"
msgstr[0] "użytkownik"
msgstr[1] "użytkowników"
msgstr[2] "użytkowników"
msgstr[3] "użytkownika"

#: CTFd/utils/modes/__init__.py:37
msgid "team"
msgid_plural "teams"
msgstr[0] "zespół"
msgstr[1] "zespoły"
msgstr[2] "zespołów"
msgstr[3] "zespołu"


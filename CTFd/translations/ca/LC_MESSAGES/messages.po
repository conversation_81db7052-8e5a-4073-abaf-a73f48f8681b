msgid ""
msgstr ""
"Project-Id-Version: CTFd\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-04-02 07:37-0400\n"
"PO-Revision-Date: 2025-09-24 00:30\n"
"Last-Translator: \n"
"Language-Team: Catalan\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: CTFd\n"
"X-Crowdin-Project-ID: 659064\n"
"X-Crowdin-Language: ca\n"
"X-Crowdin-File: /master/messages.pot\n"
"X-Crowdin-File-ID: 188\n"
"Language: ca_ES\n"

#: CTFd/challenges.py:38
msgid "Challenge Visibility is set to Admins Only"
msgstr ""

#: CTFd/challenges.py:41 CTFd/utils/decorators/__init__.py:39
#, python-format
msgid "%(ctf_name)s has not started yet"
msgstr ""

#: CTFd/challenges.py:44
#, python-format
msgid "%(ctf_name)s is paused"
msgstr ""

#: CTFd/challenges.py:47 CTFd/utils/decorators/__init__.py:31
#, python-format
msgid "%(ctf_name)s has ended"
msgstr ""

#: CTFd/forms/auth.py:21 CTFd/forms/self.py:21
#: CTFd/themes/core-beta/templates/teams/private.html:329
#: CTFd/themes/core-beta/templates/teams/public.html:95
msgid "User Name"
msgstr "Nom d'usuari"

#: CTFd/forms/auth.py:23 CTFd/forms/auth.py:60 CTFd/forms/self.py:22
#: CTFd/forms/teams.py:230
msgid "Email"
msgstr "Correu electrònic"

#: CTFd/forms/auth.py:24 CTFd/forms/auth.py:50 CTFd/forms/auth.py:67
#: CTFd/forms/self.py:24 CTFd/forms/teams.py:231
msgid "Password"
msgstr "Contrasenya"

#: CTFd/forms/auth.py:25 CTFd/forms/auth.py:51 CTFd/forms/auth.py:62
#: CTFd/forms/auth.py:69 CTFd/forms/self.py:29 CTFd/forms/teams.py:154
#: CTFd/forms/teams.py:237 CTFd/themes/core-beta/templates/challenge.html:147
msgid "Submit"
msgstr "Envia"

#: CTFd/forms/auth.py:46
msgid "User Name or Email"
msgstr "Nom d'usuari o correu electrònic"

#: CTFd/forms/auth.py:55
msgid "Resend Confirmation Email"
msgstr "Reenvia el correu de confirmació"

#: CTFd/forms/self.py:23 CTFd/forms/users.py:181
msgid "Language"
msgstr "Idioma"

#: CTFd/forms/self.py:25
msgid "Current Password"
msgstr "Contrasenya actual"

#: CTFd/forms/self.py:26 CTFd/forms/teams.py:140 CTFd/forms/teams.py:218
#: CTFd/forms/teams.py:233 CTFd/forms/users.py:164
#: CTFd/themes/core-beta/templates/teams/teams.html:51
#: CTFd/themes/core-beta/templates/users/users.html:48
msgid "Affiliation"
msgstr "Afiliació"

#: CTFd/forms/self.py:27 CTFd/forms/teams.py:146 CTFd/forms/teams.py:219
#: CTFd/forms/teams.py:232 CTFd/forms/users.py:165
#: CTFd/themes/core-beta/templates/teams/teams.html:50
#: CTFd/themes/core-beta/templates/users/users.html:47
msgid "Website"
msgstr "Lloc web"

#: CTFd/forms/self.py:28 CTFd/forms/teams.py:150 CTFd/forms/teams.py:234
#: CTFd/themes/core-beta/templates/teams/teams.html:52
#: CTFd/themes/core-beta/templates/users/users.html:49
msgid "Country"
msgstr "País"

#: CTFd/forms/self.py:59 CTFd/themes/core-beta/templates/settings.html:225
msgid "Expiration"
msgstr "Venciment"

#: CTFd/forms/self.py:61
msgid "Generate"
msgstr "Genera"

#: CTFd/forms/setup.py:29
msgid "Event Name"
msgstr "Nom de l'esdeveniment"

#: CTFd/forms/setup.py:29
msgid "The name of your CTF event/workshop"
msgstr "El nom del teu esdeveniment/taller CTF"

#: CTFd/forms/setup.py:32
msgid "Event Description"
msgstr "Descripció de l'esdeveniment"

#: CTFd/forms/setup.py:32
msgid "Description for the CTF"
msgstr "Descripció del CTF"

#: CTFd/forms/setup.py:35 CTFd/forms/setup.py:36
msgid "User Mode"
msgstr "Mode Usuari"

#: CTFd/forms/setup.py:36
msgid "Team Mode"
msgstr "Mode Equip"

#: CTFd/forms/setup.py:39
msgid "Controls whether users join together in teams to play (Team Mode) or play as themselves (User Mode)"
msgstr "Controla si els usuaris es reuneixen en equips per jugar (Mode Equip) o juguen com a individus (Mode Usuari)"

#: CTFd/forms/setup.py:45
msgid "Admin Username"
msgstr "Nom d'usuari de l'administrador"

#: CTFd/forms/setup.py:46
msgid "Your username for the administration account"
msgstr "El teu nom d'usuari per al compte d'administració"

#: CTFd/forms/setup.py:50
msgid "Admin Email"
msgstr "Correu electrònic de l'administrador"

#: CTFd/forms/setup.py:51
msgid "Your email address for the administration account"
msgstr "La teva adreça de correu electrònic per al compte d'administració"

#: CTFd/forms/setup.py:55
msgid "Admin Password"
msgstr "Contrasenya de l'administrador"

#: CTFd/forms/setup.py:56
msgid "Your password for the administration account"
msgstr "La teva contrasenya per al compte d'administració"

#: CTFd/forms/setup.py:61
msgid "Logo"
msgstr "Logotip"

#: CTFd/forms/setup.py:63
msgid "Logo to use for the website instead of a CTF name. Used as the home page button. Optional."
msgstr "Logotip a utilitzar per al lloc web en lloc del nom del CTF. Utilitzat com a botó de la pàgina principal. Opcional."

#: CTFd/forms/setup.py:67
msgid "Banner"
msgstr "Banner"

#: CTFd/forms/setup.py:67
msgid "Banner to use for the homepage. Optional."
msgstr "Banner a utilitzar per a la pàgina d'inici. Opcional."

#: CTFd/forms/setup.py:70
msgid "Small Icon"
msgstr "Icona Petita"

#: CTFd/forms/setup.py:72
msgid "favicon used in user's browsers. Only PNGs accepted. Must be 32x32px. Optional."
msgstr "favicon utilitzat als navegadors dels usuaris. Només s'accepten PNGs. Ha de ser de 32x32px. Opcional."

#: CTFd/forms/setup.py:76
msgid "Theme"
msgstr "Tema"

#: CTFd/forms/setup.py:77
msgid "CTFd Theme to use. Can be changed later."
msgstr "Tema de CTFd a utilitzar. Es pot canviar més endavant."

#: CTFd/forms/setup.py:84
msgid "Theme Color"
msgstr "Color del Tema"

#: CTFd/forms/setup.py:86
msgid "Color used by theme to control aesthetics. Requires theme support. Optional."
msgstr "Color utilitzat pel tema per controlar l'estètica. Requereix suport del tema. Opcional."

#: CTFd/forms/setup.py:91
msgid "Verify Emails"
msgstr "Verificar Correus Electrònics"

#: CTFd/forms/setup.py:143
msgid "Start Time"
msgstr "Hora d'Inici"

#: CTFd/forms/setup.py:144
msgid "Time when your CTF is scheduled to start. Optional."
msgstr "Hora en què està programat que comenci el teu CTF. Opcional."

#: CTFd/forms/setup.py:147
msgid "End Time"
msgstr "Hola de Fi"

#: CTFd/forms/setup.py:148
msgid "Time when your CTF is scheduled to end. Optional."
msgstr "Hora en què està programat que finalitzi el teu CTF. Opcional."

#: CTFd/forms/setup.py:150
msgid "Finish"
msgstr "Finalitzar"

#: CTFd/forms/teams.py:102 CTFd/forms/teams.py:109 CTFd/forms/teams.py:127
#: CTFd/forms/teams.py:229
msgid "Team Name"
msgstr "Nom de l'Equip"

#: CTFd/forms/teams.py:103 CTFd/forms/teams.py:110
msgid "Team Password"
msgstr "Contrasenya de l'Equip"

#: CTFd/forms/teams.py:104 CTFd/forms/teams.py:289
msgid "Join"
msgstr "Uneix-te"

#: CTFd/forms/teams.py:111
msgid "Create"
msgstr "Crea"

#: CTFd/forms/teams.py:128
msgid "Your team's public name shown to other competitors"
msgstr "El nom públic del teu equip mostrat a altres competidors"

#: CTFd/forms/teams.py:131
msgid "New Team Password"
msgstr "Nova contrasenya de l'equip"

#: CTFd/forms/teams.py:131
msgid "Set a new team join password"
msgstr "Estableix una nova contrasenya per unir-se a l'equip"

#: CTFd/forms/teams.py:134
msgid "Confirm Current Team Password"
msgstr "Confirma la contrasenya actual de l'equip"

#: CTFd/forms/teams.py:136
msgid "Provide your current team password (or your password) to update your team's password"
msgstr "Proporciona la contrasenya actual de l'equip (o la teva) per actualitzar la contrasenya de l'equip"

#: CTFd/forms/teams.py:142
msgid "Your team's affiliation publicly shown to other competitors"
msgstr "L'afiliació del teu equip mostrada públicament a altres competidors"

#: CTFd/forms/teams.py:147
msgid "Your team's website publicly shown to other competitors"
msgstr "El lloc web del teu equip mostrat públicament a altres competidors"

#: CTFd/forms/teams.py:152
msgid "Your team's country publicly shown to other competitors"
msgstr "El país del teu equip mostrat públicament a altres competidors"

#: CTFd/forms/teams.py:192
msgid "Team Captain"
msgstr "Capità de l'equip"

#: CTFd/forms/teams.py:215 CTFd/forms/users.py:161
msgid "Search Field"
msgstr "Camp de cerca"

#: CTFd/forms/teams.py:217 CTFd/forms/users.py:163
#: CTFd/themes/core-beta/templates/challenge.html:206
msgid "Name"
msgstr "Nom"

#: CTFd/forms/teams.py:224 CTFd/forms/users.py:171
msgid "Parameter"
msgstr "Paràmetre"

#: CTFd/forms/teams.py:225 CTFd/forms/users.py:175
msgid "Search"
msgstr "Cerca"

#: CTFd/forms/teams.py:235
msgid "Hidden"
msgstr "Ocult"

#: CTFd/forms/teams.py:236
msgid "Banned"
msgstr "Prohibit"

#: CTFd/forms/teams.py:285
msgid "Invite Link"
msgstr "Enllaç d'invitació"

#: CTFd/forms/users.py:133
msgid "Bracket"
msgstr ""

#: CTFd/forms/users.py:134
msgid "Competition bracket for your user"
msgstr ""

#: CTFd/forms/users.py:172
msgid "Search for matching users"
msgstr "Cerca usuaris coincidents"

#: CTFd/themes/core-beta/templates/base.html:53
msgid "Powered by CTFd"
msgstr "Impulsat per CTFd"

#: CTFd/themes/core-beta/templates/challenge.html:11
#: CTFd/themes/core-beta/templates/teams/private.html:388
#: CTFd/themes/core-beta/templates/teams/public.html:157
#: CTFd/themes/core-beta/templates/users/private.html:124
#: CTFd/themes/core-beta/templates/users/public.html:123
msgid "Challenge"
msgstr "Desafiament"

#: CTFd/themes/core-beta/templates/challenge.html:19
#, python-format
msgid "%(num)d Solve"
msgid_plural "%(num)d Solves"
msgstr[0] "%(num)d Solució"
msgstr[1] "%(num)d Solucions"

#: CTFd/themes/core-beta/templates/challenge.html:77
msgid "View Hint"
msgstr "Veure Pista"

#: CTFd/themes/core-beta/templates/challenge.html:120
msgid "attempt"
msgid_plural "attempts"
msgstr[0] ""
msgstr[1] ""

#: CTFd/themes/core-beta/templates/challenge.html:135
msgid "Flag"
msgstr "Bandera"

#: CTFd/themes/core-beta/templates/challenge.html:169
msgid "Next Challenge"
msgstr "Pròxim Desafiament"

#: CTFd/themes/core-beta/templates/challenge.html:175
msgid "Share"
msgstr ""

#: CTFd/themes/core-beta/templates/challenge.html:207
#: CTFd/themes/core-beta/templates/setup.html:305
#: CTFd/themes/core-beta/templates/setup.html:326
msgid "Date"
msgstr "Data"

#: CTFd/themes/core-beta/templates/challenges.html:7
#: CTFd/themes/core-beta/templates/components/navbar.html:59
msgid "Challenges"
msgstr "Reptes"

#: CTFd/themes/core-beta/templates/confirm.html:7
msgid "Confirm"
msgstr "Confirma"

#: CTFd/themes/core-beta/templates/confirm.html:18
msgid "We've sent a confirmation email to your email address."
msgstr "Hem enviat un correu electrònic de confirmació a la teva adreça."

#: CTFd/themes/core-beta/templates/confirm.html:24
msgid "Please click the link in that email to confirm your account."
msgstr "Si us plau, fes clic a l'enllaç d'aquest correu per confirmar el teu compte."

#: CTFd/themes/core-beta/templates/confirm.html:30
msgid "If the email doesn’t arrive, check your spam folder or contact an administrator to manually verify your account."
msgstr "Si el correu no arriba, revisa la carpeta de correu brossa o contacta amb un administrador per verificar manualment el teu compte."

#: CTFd/themes/core-beta/templates/confirm.html:43
msgid "Change Email Address"
msgstr "Canvia l'adreça de correu electrònic"

#: CTFd/themes/core-beta/templates/components/navbar.html:164
#: CTFd/themes/core-beta/templates/components/navbar.html:169
#: CTFd/themes/core-beta/templates/login.html:7
msgid "Login"
msgstr "Inicia sessió"

#: CTFd/themes/core-beta/templates/login.html:40
msgid "Forgot your password?"
msgstr "Has oblidat la teva contrasenya?"

#: CTFd/themes/core-beta/templates/components/navbar.html:85
#: CTFd/themes/core-beta/templates/components/navbar.html:91
#: CTFd/themes/core-beta/templates/notifications.html:7
msgid "Notifications"
msgstr "Notificacions"

#: CTFd/themes/core-beta/templates/notifications.html:14
msgid "There are no notifications yet"
msgstr "Encara no hi ha notificacions"

#: CTFd/themes/core-beta/templates/components/navbar.html:151
#: CTFd/themes/core-beta/templates/components/navbar.html:156
#: CTFd/themes/core-beta/templates/register.html:7
msgid "Register"
msgstr "Registra't"

#: CTFd/themes/core-beta/templates/register.html:35
msgid "Your username on the site"
msgstr "El teu nom d'usuari al lloc"

#: CTFd/themes/core-beta/templates/register.html:43
msgid "Never shown to the public"
msgstr "Mai es mostra al públic"

#: CTFd/themes/core-beta/templates/register.html:51
msgid "Password used to log into your account"
msgstr "Contrasenya utilitzada per iniciar sessió al teu compte"

#: CTFd/themes/core-beta/templates/register.html:69
#, python-format
msgid "By registering, you agree to the <a href=\"%(privacy_link)s\" target=\"_blank\">privacy policy</a> and <a href=\"%(tos_link)s\" target=\"_blank\">terms of service</a>"
msgstr ""

#: CTFd/themes/core-beta/templates/reset_password.html:7
msgid "Reset Password"
msgstr "Restableix la contrasenya"

#: CTFd/themes/core-beta/templates/reset_password.html:21
msgid "You can now reset the password for your account and log in. Please enter in a new password below."
msgstr "Ara pots restablir la contrasenya del teu compte i iniciar sessió. Si us plau, introdueix una nova contrasenya a continuació."

#: CTFd/themes/core-beta/templates/reset_password.html:44
msgid "Please provide the email address associated with your account below."
msgstr "Si us plau, proporciona a continuació l'adreça de correu electrònic associada al teu compte."

#: CTFd/themes/core-beta/templates/components/navbar.html:52
#: CTFd/themes/core-beta/templates/scoreboard.html:7
msgid "Scoreboard"
msgstr "Marcador"

#: CTFd/themes/core-beta/templates/scoreboard.html:24
msgid "All"
msgstr ""

#: CTFd/themes/core-beta/templates/scoreboard.html:36
msgid "Place"
msgstr ""

#: CTFd/themes/core-beta/templates/scoreboard.html:38
#: CTFd/themes/core-beta/templates/teams/private.html:330
#: CTFd/themes/core-beta/templates/teams/public.html:96
msgid "Score"
msgstr "Puntuació"

#: CTFd/themes/core-beta/templates/scoreboard.html:60
msgid "Scoreboard is empty"
msgstr ""

#: CTFd/themes/core-beta/templates/components/navbar.html:124
#: CTFd/themes/core-beta/templates/components/navbar.html:129
#: CTFd/themes/core-beta/templates/settings.html:8
#: CTFd/themes/core-beta/templates/setup.html:50
msgid "Settings"
msgstr "Configuració"

#: CTFd/themes/core-beta/templates/components/navbar.html:112
#: CTFd/themes/core-beta/templates/components/navbar.html:117
#: CTFd/themes/core-beta/templates/settings.html:21
msgid "Profile"
msgstr "Perfil"

#: CTFd/themes/core-beta/templates/settings.html:26
msgid "Access Tokens"
msgstr "Tokens d'accés"

#: CTFd/themes/core-beta/templates/settings.html:95
msgid "Your profile has been updated"
msgstr "El teu perfil ha estat actualitzat"

#: CTFd/themes/core-beta/templates/settings.html:103
#: CTFd/themes/core-beta/templates/teams/private.html:83
#: CTFd/themes/core-beta/templates/teams/private.html:198
msgid "Error:"
msgstr "Error:"

#: CTFd/themes/core-beta/templates/settings.html:129
msgid "API Key Generated"
msgstr "Clau API generada"

#: CTFd/themes/core-beta/templates/settings.html:137
msgid "Please copy your API Key, it won't be shown again!"
msgstr "Si us plau, copia la teva clau API, no es tornarà a mostrar!"

#: CTFd/themes/core-beta/templates/settings.html:183
msgid "Active Tokens"
msgstr "Tokens actius"

#: CTFd/themes/core-beta/templates/settings.html:195
msgid "Delete Token"
msgstr "Elimina Token"

#: CTFd/themes/core-beta/templates/settings.html:204
msgid "Are you sure you want to delete this token?"
msgstr "Estàs segur que vols eliminar aquest token?"

#: CTFd/themes/core-beta/templates/settings.html:224
msgid "Created"
msgstr "Creat"

#: CTFd/themes/core-beta/templates/settings.html:226
msgid "Description"
msgstr "Descripció"

#: CTFd/themes/core-beta/templates/settings.html:227
msgid "Delete"
msgstr "Elimina"

#: CTFd/themes/core-beta/templates/setup.html:24
msgid "Setup"
msgstr "Configuració"

#: CTFd/themes/core-beta/templates/setup.html:44
msgid "General"
msgstr "General"

#: CTFd/themes/core-beta/templates/setup.html:47
msgid "Mode"
msgstr "Mode"

#: CTFd/themes/core-beta/templates/setup.html:53
msgid "Administration"
msgstr "Administració"

#: CTFd/themes/core-beta/templates/setup.html:56
msgid "Style"
msgstr "Estil"

#: CTFd/themes/core-beta/templates/setup.html:59
msgid "Date &amp; Time"
msgstr "Data &amp; hora"

#: CTFd/themes/core-beta/templates/setup.html:62
msgid "Integrations"
msgstr "Integracions"

#: CTFd/themes/core-beta/templates/setup.html:86
#: CTFd/themes/core-beta/templates/setup.html:138
#: CTFd/themes/core-beta/templates/setup.html:202
#: CTFd/themes/core-beta/templates/setup.html:238
#: CTFd/themes/core-beta/templates/setup.html:296
#: CTFd/themes/core-beta/templates/setup.html:345
msgid "Next"
msgstr "Següent"

#: CTFd/themes/core-beta/templates/setup.html:112
msgid "Participants register accounts and form teams"
msgstr "Els participants registren comptes i formen equips"

#: CTFd/themes/core-beta/templates/setup.html:113
msgid "If a team member solves a challenge, the entire team receives credit"
msgstr "Si un membre de l'equip resol un desafiament, tot l'equip rep crèdit"

#: CTFd/themes/core-beta/templates/setup.html:115
msgid "Easier to see which team member solved a challenge"
msgstr "És més fàcil veure quin membre de l'equip ha resolt un desafiament"

#: CTFd/themes/core-beta/templates/setup.html:116
msgid "May be slightly more difficult to administer"
msgstr "Pot ser una mica més difícil d'administrar"

#: CTFd/themes/core-beta/templates/setup.html:120
msgid "Participants only register an individual account"
msgstr "Els participants només registren un compte individual"

#: CTFd/themes/core-beta/templates/setup.html:121
msgid "Players can share accounts to form pseudo-teams"
msgstr "Els jugadors poden compartir comptes per formar pseudo-equips"

#: CTFd/themes/core-beta/templates/setup.html:123
msgid "Easier to organize"
msgstr "Més fàcil d'organitzar"

#: CTFd/themes/core-beta/templates/setup.html:124
msgid "Difficult to attribute solutions to individual team members"
msgstr "Difícil atribuir les solucions als membres individuals de l'equip"

#: CTFd/themes/core-beta/templates/setup.html:143
msgid "Visibility Settings"
msgstr "Configuració de Visibilitat"

#: CTFd/themes/core-beta/templates/setup.html:146
msgid "Control the visibility of different sections of CTFd"
msgstr "Controla la visibilitat de diferents seccions de CTFd"

#: CTFd/themes/core-beta/templates/setup.html:232
msgid "Subscribe email address to the CTFd LLC Newsletter for news and updates"
msgstr "Subscriu l'adreça de correu electrònic al butlletí de CTFd LLC per a notícies i actualitzacions"

#: CTFd/themes/core-beta/templates/setup.html:309
#: CTFd/themes/core-beta/templates/setup.html:330
#: CTFd/themes/core-beta/templates/teams/private.html:391
#: CTFd/themes/core-beta/templates/teams/public.html:160
#: CTFd/themes/core-beta/templates/users/private.html:127
#: CTFd/themes/core-beta/templates/users/public.html:126
msgid "Time"
msgstr "Hora"

#: CTFd/themes/core-beta/templates/setup.html:334
msgid "UTC Preview"
msgstr "Previsualització UTC"

#: CTFd/themes/core-beta/templates/components/navbar.html:36
#: CTFd/themes/core-beta/templates/users/users.html:6
msgid "Users"
msgstr "Usuaris"

#: CTFd/themes/core-beta/templates/components/navbar.html:43
#: CTFd/themes/core-beta/templates/teams/teams.html:6
msgid "Teams"
msgstr "Equips"

#: CTFd/themes/core-beta/templates/components/navbar.html:72
#: CTFd/themes/core-beta/templates/components/navbar.html:77
msgid "Admin Panel"
msgstr "Panell d'Administració"

#: CTFd/themes/core-beta/templates/components/navbar.html:99
#: CTFd/themes/core-beta/templates/components/navbar.html:104
#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:6
#: CTFd/themes/core-beta/templates/teams/teams.html:49
msgid "Team"
msgstr "Equip"

#: CTFd/themes/core-beta/templates/components/navbar.html:136
#: CTFd/themes/core-beta/templates/components/navbar.html:141
msgid "Logout"
msgstr "Sortir"

#: CTFd/themes/core-beta/templates/components/navbar.html:180
msgid "Change language"
msgstr ""

#: CTFd/themes/core-beta/templates/components/navbar.html:199
#: CTFd/themes/core-beta/templates/components/navbar.html:204
msgid "Toggle theme"
msgstr ""

#: CTFd/themes/core-beta/templates/errors/403.html:12
msgid "403 Forbidden"
msgstr ""

#: CTFd/themes/core-beta/templates/errors/404.html:9
msgid "File not found"
msgstr "Fitxer no trobat"

#: CTFd/themes/core-beta/templates/errors/404.html:12
msgid "404 Not Found"
msgstr ""

#: CTFd/themes/core-beta/templates/errors/429.html:9
msgid "Too many requests"
msgstr "Massa peticions"

#: CTFd/themes/core-beta/templates/errors/429.html:10
msgid "Please slow down!"
msgstr "Si us plau, afluixa el ritme!"

#: CTFd/themes/core-beta/templates/errors/429.html:13
msgid "429 Too Many Requests"
msgstr ""

#: CTFd/themes/core-beta/templates/errors/502.html:12
msgid "502 Bad Gateway"
msgstr ""

#: CTFd/themes/core-beta/templates/macros/forms.html:13
#: CTFd/themes/core-beta/templates/macros/forms.html:36
#: CTFd/themes/core-beta/templates/macros/forms.html:65
msgid "(Optional)"
msgstr "(Opcional)"

#: CTFd/themes/core-beta/templates/teams/invite.html:6
#: CTFd/themes/core-beta/templates/teams/join_team.html:6
#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:36
msgid "Join Team"
msgstr "Uneix-te a l'equip"

#: CTFd/themes/core-beta/templates/teams/invite.html:15
#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:12
msgid "Welcome to"
msgstr "Benvingut a"

#: CTFd/themes/core-beta/templates/teams/invite.html:19
msgid "Click the button below to join the team!"
msgstr "Fes clic al botó de sota per unir-te a l'equip!"

#: CTFd/themes/core-beta/templates/teams/new_team.html:6
#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:39
msgid "Create Team"
msgstr "Crea l'equip"

#: CTFd/themes/core-beta/templates/teams/private.html:8
msgid "Edit Team"
msgstr "Edita l'equip"

#: CTFd/themes/core-beta/templates/teams/private.html:76
msgid "Your team's profile has been updated"
msgstr "El perfil del teu equip s'ha actualitzat"

#: CTFd/themes/core-beta/templates/teams/private.html:104
msgid "Choose Captain"
msgstr "Tria Capità"

#: CTFd/themes/core-beta/templates/teams/private.html:127
msgid "Your captain rights have been transferred"
msgstr "Els teus drets de capità han estat transferits"

#: CTFd/themes/core-beta/templates/teams/private.html:152
msgid "Invite Users"
msgstr "Convida usuaris"

#: CTFd/themes/core-beta/templates/teams/private.html:171
msgid "Share this link with your team members for them to join your team"
msgstr "Comparteix aquest enllaç amb els membres del teu equip perquè s'uneixin"

#: CTFd/themes/core-beta/templates/teams/private.html:174
msgid "Invite links can be re-used and expire after 1 day"
msgstr "Els enllaços d'invitació es poden reutilitzar i caduquen després d'un dia"

#: CTFd/themes/core-beta/templates/teams/private.html:188
msgid "Disband Team"
msgstr "Dissol l'equip"

#: CTFd/themes/core-beta/templates/teams/private.html:193
msgid "Are you sure you want to disband your team?"
msgstr "Estàs segur que vols dissoldre el teu equip?"

#: CTFd/themes/core-beta/templates/teams/private.html:206
msgid "No"
msgstr "No"

#: CTFd/themes/core-beta/templates/teams/private.html:207
msgid "Yes"
msgstr "Si"

#: CTFd/themes/core-beta/templates/teams/private.html:218
#: CTFd/themes/core-beta/templates/teams/public.html:10
#: CTFd/themes/core-beta/templates/users/private.html:21
#: CTFd/themes/core-beta/templates/users/public.html:21
#: CTFd/themes/core-beta/templates/users/users.html:70
msgid "Official"
msgstr "Oficial"

#: CTFd/themes/core-beta/templates/teams/private.html:260
msgid "place"
msgstr "lloc"

#: CTFd/themes/core-beta/templates/teams/private.html:268
msgid "points"
msgstr "punts"

#: CTFd/themes/core-beta/templates/teams/private.html:325
#: CTFd/themes/core-beta/templates/teams/public.html:91
msgid "Members"
msgstr "Membres"

#: CTFd/themes/core-beta/templates/teams/private.html:345
#: CTFd/themes/core-beta/templates/teams/public.html:110
msgid "Captain"
msgstr "Capità"

#: CTFd/themes/core-beta/templates/teams/private.html:361
#: CTFd/themes/core-beta/templates/teams/public.html:125
#: CTFd/themes/core-beta/templates/users/private.html:98
#: CTFd/themes/core-beta/templates/users/public.html:98
msgid "Awards"
msgstr "Premis"

#: CTFd/themes/core-beta/templates/teams/private.html:384
#: CTFd/themes/core-beta/templates/teams/public.html:153
#: CTFd/themes/core-beta/templates/users/private.html:120
msgid "Solves"
msgstr "Solucions"

#: CTFd/themes/core-beta/templates/teams/private.html:389
#: CTFd/themes/core-beta/templates/teams/public.html:158
#: CTFd/themes/core-beta/templates/users/private.html:125
#: CTFd/themes/core-beta/templates/users/public.html:124
msgid "Category"
msgstr "Categoria"

#: CTFd/themes/core-beta/templates/teams/private.html:390
#: CTFd/themes/core-beta/templates/teams/public.html:159
#: CTFd/themes/core-beta/templates/users/private.html:126
#: CTFd/themes/core-beta/templates/users/public.html:125
msgid "Value"
msgstr "Valor"

#: CTFd/themes/core-beta/templates/teams/private.html:481
#: CTFd/themes/core-beta/templates/teams/public.html:248
#: CTFd/themes/core-beta/templates/users/private.html:213
#: CTFd/themes/core-beta/templates/users/public.html:214
msgid "No solves yet"
msgstr "Encara no hi ha solucions"

#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:14
msgid "In order to participate you must either join or create a team."
msgstr "Per participar cal que t'uneixis o crear un equip."

#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:22
msgid "Play with Official Team"
msgstr "Juga amb l'Equip Oficial"

#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:27
msgid "Join Unofficial Team"
msgstr "Uneix-te a un Equip No Oficial"

#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:30
msgid "Create Unofficial Team"
msgstr "Crea un Equip No Oficial"

#: CTFd/themes/core-beta/templates/teams/teams.html:128
#: CTFd/themes/core-beta/templates/users/users.html:118
msgid "Page"
msgstr "Pàgina"

#: CTFd/themes/core-beta/templates/users/users.html:14
#, python-format
msgid "Searching for users with <strong>%(field)s</strong> matching <strong>%(q)s</strong>"
msgstr ""

#: CTFd/themes/core-beta/templates/users/users.html:17
#, python-format
msgid "Page %(page)s of %(total)s results"
msgstr ""

#: CTFd/themes/core-beta/templates/users/users.html:46
msgid "User"
msgstr "Usuari"

#: CTFd/utils/modes/__init__.py:35
msgid "user"
msgid_plural "users"
msgstr[0] "usuari"
msgstr[1] "usuaris"

#: CTFd/utils/modes/__init__.py:37
msgid "team"
msgid_plural "teams"
msgstr[0] "equip"
msgstr[1] "equips"


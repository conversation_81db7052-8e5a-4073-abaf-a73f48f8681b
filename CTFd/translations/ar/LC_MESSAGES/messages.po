msgid ""
msgstr ""
"Project-Id-Version: CTFd\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-04-02 07:37-0400\n"
"PO-Revision-Date: 2025-09-24 00:30\n"
"Last-Translator: \n"
"Language-Team: Arabic\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"
"Plural-Forms: nplurals=6; plural=(n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5);\n"
"X-Crowdin-Project: CTFd\n"
"X-Crowdin-Project-ID: 659064\n"
"X-Crowdin-Language: ar\n"
"X-Crowdin-File: /master/messages.pot\n"
"X-Crowdin-File-ID: 188\n"
"Language: ar_SA\n"

#: CTFd/challenges.py:38
msgid "Challenge Visibility is set to Admins Only"
msgstr ""

#: CTFd/challenges.py:41 CTFd/utils/decorators/__init__.py:39
#, python-format
msgid "%(ctf_name)s has not started yet"
msgstr ""

#: CTFd/challenges.py:44
#, python-format
msgid "%(ctf_name)s is paused"
msgstr ""

#: CTFd/challenges.py:47 CTFd/utils/decorators/__init__.py:31
#, python-format
msgid "%(ctf_name)s has ended"
msgstr ""

#: CTFd/forms/auth.py:21 CTFd/forms/self.py:21
#: CTFd/themes/core-beta/templates/teams/private.html:329
#: CTFd/themes/core-beta/templates/teams/public.html:95
msgid "User Name"
msgstr "اسم المستخدم"

#: CTFd/forms/auth.py:23 CTFd/forms/auth.py:60 CTFd/forms/self.py:22
#: CTFd/forms/teams.py:230
msgid "Email"
msgstr "البريد الإلكتروني"

#: CTFd/forms/auth.py:24 CTFd/forms/auth.py:50 CTFd/forms/auth.py:67
#: CTFd/forms/self.py:24 CTFd/forms/teams.py:231
msgid "Password"
msgstr "كلمة المرور"

#: CTFd/forms/auth.py:25 CTFd/forms/auth.py:51 CTFd/forms/auth.py:62
#: CTFd/forms/auth.py:69 CTFd/forms/self.py:29 CTFd/forms/teams.py:154
#: CTFd/forms/teams.py:237 CTFd/themes/core-beta/templates/challenge.html:147
msgid "Submit"
msgstr "إرسال"

#: CTFd/forms/auth.py:46
msgid "User Name or Email"
msgstr "اسم المستخدم أو البريد الإلكتروني"

#: CTFd/forms/auth.py:55
msgid "Resend Confirmation Email"
msgstr "إعادة إرسال رسالة التأكيد"

#: CTFd/forms/self.py:23 CTFd/forms/users.py:181
msgid "Language"
msgstr "اللغة"

#: CTFd/forms/self.py:25
msgid "Current Password"
msgstr "كلمة المرور الحالية"

#: CTFd/forms/self.py:26 CTFd/forms/teams.py:140 CTFd/forms/teams.py:218
#: CTFd/forms/teams.py:233 CTFd/forms/users.py:164
#: CTFd/themes/core-beta/templates/teams/teams.html:51
#: CTFd/themes/core-beta/templates/users/users.html:48
msgid "Affiliation"
msgstr "الجهة"

#: CTFd/forms/self.py:27 CTFd/forms/teams.py:146 CTFd/forms/teams.py:219
#: CTFd/forms/teams.py:232 CTFd/forms/users.py:165
#: CTFd/themes/core-beta/templates/teams/teams.html:50
#: CTFd/themes/core-beta/templates/users/users.html:47
msgid "Website"
msgstr "الموقع الإلكتروني"

#: CTFd/forms/self.py:28 CTFd/forms/teams.py:150 CTFd/forms/teams.py:234
#: CTFd/themes/core-beta/templates/teams/teams.html:52
#: CTFd/themes/core-beta/templates/users/users.html:49
msgid "Country"
msgstr "الدولة"

#: CTFd/forms/self.py:59 CTFd/themes/core-beta/templates/settings.html:225
msgid "Expiration"
msgstr "وقت الانتهاء"

#: CTFd/forms/self.py:61
msgid "Generate"
msgstr "إنشاء"

#: CTFd/forms/setup.py:29
msgid "Event Name"
msgstr "اسم الحدث"

#: CTFd/forms/setup.py:29
msgid "The name of your CTF event/workshop"
msgstr "اسم مسابقة التقط العلم / ورشة العمل"

#: CTFd/forms/setup.py:32
msgid "Event Description"
msgstr "وصف الحدث"

#: CTFd/forms/setup.py:32
msgid "Description for the CTF"
msgstr "وصف مسابقة التقط العلم"

#: CTFd/forms/setup.py:35 CTFd/forms/setup.py:36
msgid "User Mode"
msgstr "فردي"

#: CTFd/forms/setup.py:36
msgid "Team Mode"
msgstr "فريق"

#: CTFd/forms/setup.py:39
msgid "Controls whether users join together in teams to play (Team Mode) or play as themselves (User Mode)"
msgstr "يتحكم فيما إذا كانت المسابقة بشكل جماعي (فريق) أو بشكل منفرد (فردي)"

#: CTFd/forms/setup.py:45
msgid "Admin Username"
msgstr "اسم مستخدم المسؤول"

#: CTFd/forms/setup.py:46
msgid "Your username for the administration account"
msgstr "اسم المستخدم لحساب المسؤول"

#: CTFd/forms/setup.py:50
msgid "Admin Email"
msgstr "بريد المسؤول الإلكتروني"

#: CTFd/forms/setup.py:51
msgid "Your email address for the administration account"
msgstr "بريدك الإلكتروني لحساب المسؤول"

#: CTFd/forms/setup.py:55
msgid "Admin Password"
msgstr "كلمة مرور المسؤول"

#: CTFd/forms/setup.py:56
msgid "Your password for the administration account"
msgstr "كلمة مرورك لحساب المسؤول"

#: CTFd/forms/setup.py:61
msgid "Logo"
msgstr "الشعار"

#: CTFd/forms/setup.py:63
msgid "Logo to use for the website instead of a CTF name. Used as the home page button. Optional."
msgstr "الشعار المستخدم في الموقع بدل الاسم. يأخذ المستخدم للصفحة الرئيسية. اختياري."

#: CTFd/forms/setup.py:67
msgid "Banner"
msgstr "الترويسة"

#: CTFd/forms/setup.py:67
msgid "Banner to use for the homepage. Optional."
msgstr "الترويسة المستخدمة في الصفحة الرئيسية. اختياري."

#: CTFd/forms/setup.py:70
msgid "Small Icon"
msgstr "شعار صغير"

#: CTFd/forms/setup.py:72
msgid "favicon used in user's browsers. Only PNGs accepted. Must be 32x32px. Optional."
msgstr "شعار favicon المستخدم في تبويب المتصفح. صيغة PNG فقط. يجب أن تكون أبعادها 32x32 بكسل. اختياري."

#: CTFd/forms/setup.py:76
msgid "Theme"
msgstr "السمة"

#: CTFd/forms/setup.py:77
msgid "CTFd Theme to use. Can be changed later."
msgstr "السمة المستخدمة. يمكن تغييرها لاحقًا."

#: CTFd/forms/setup.py:84
msgid "Theme Color"
msgstr "لون السمة"

#: CTFd/forms/setup.py:86
msgid "Color used by theme to control aesthetics. Requires theme support. Optional."
msgstr "اللون المستخدم في السمة بشكل جمالي. يتطلب دعم السمة. اختياري."

#: CTFd/forms/setup.py:91
msgid "Verify Emails"
msgstr "التحقق من رسائل البريد الإلكتروني"

#: CTFd/forms/setup.py:143
msgid "Start Time"
msgstr "وقت البدء"

#: CTFd/forms/setup.py:144
msgid "Time when your CTF is scheduled to start. Optional."
msgstr "الوقت المجدول لبدء مسابقة التقط العلم. اختياري."

#: CTFd/forms/setup.py:147
msgid "End Time"
msgstr "وقت الانتهاء"

#: CTFd/forms/setup.py:148
msgid "Time when your CTF is scheduled to end. Optional."
msgstr "الوقت المجدول لانتهاء مسابقة التقط العلم. اختياري."

#: CTFd/forms/setup.py:150
msgid "Finish"
msgstr "إنهاء"

#: CTFd/forms/teams.py:102 CTFd/forms/teams.py:109 CTFd/forms/teams.py:127
#: CTFd/forms/teams.py:229
msgid "Team Name"
msgstr "اسم الفريق"

#: CTFd/forms/teams.py:103 CTFd/forms/teams.py:110
msgid "Team Password"
msgstr "كلمة مرور الفريق"

#: CTFd/forms/teams.py:104 CTFd/forms/teams.py:289
msgid "Join"
msgstr "انضمام"

#: CTFd/forms/teams.py:111
msgid "Create"
msgstr "إنشاء"

#: CTFd/forms/teams.py:128
msgid "Your team's public name shown to other competitors"
msgstr "اسم فريقك كما يظهر علنًا لبقية المتسابقين"

#: CTFd/forms/teams.py:131
msgid "New Team Password"
msgstr "كلمة مرور الفريق الجديدة"

#: CTFd/forms/teams.py:131
msgid "Set a new team join password"
msgstr "أدخل كلمة مرور للانضمام للفريق"

#: CTFd/forms/teams.py:134
msgid "Confirm Current Team Password"
msgstr "أكّد كلمة مرور الفريق الحالية"

#: CTFd/forms/teams.py:136
msgid "Provide your current team password (or your password) to update your team's password"
msgstr "أدخل كلمة مرور فريقك الحالية (أو كلمة مرورك) لتغيير كلمة مرور فريقك"

#: CTFd/forms/teams.py:142
msgid "Your team's affiliation publicly shown to other competitors"
msgstr "جهة فريقك تظهر علنًا لبقية المتسابقين"

#: CTFd/forms/teams.py:147
msgid "Your team's website publicly shown to other competitors"
msgstr "موقع فريقك الإلكتروني يظهر علنًا لبقية المتسابقين"

#: CTFd/forms/teams.py:152
msgid "Your team's country publicly shown to other competitors"
msgstr "دولة فريقك تظهر علنًا لبقية المتسابقين"

#: CTFd/forms/teams.py:192
msgid "Team Captain"
msgstr "قائد الفريق"

#: CTFd/forms/teams.py:215 CTFd/forms/users.py:161
msgid "Search Field"
msgstr "خانة البحث"

#: CTFd/forms/teams.py:217 CTFd/forms/users.py:163
#: CTFd/themes/core-beta/templates/challenge.html:206
msgid "Name"
msgstr "الاسم"

#: CTFd/forms/teams.py:224 CTFd/forms/users.py:171
msgid "Parameter"
msgstr "القيمة"

#: CTFd/forms/teams.py:225 CTFd/forms/users.py:175
msgid "Search"
msgstr "بحث"

#: CTFd/forms/teams.py:235
msgid "Hidden"
msgstr "مخفي"

#: CTFd/forms/teams.py:236
msgid "Banned"
msgstr "مطرود"

#: CTFd/forms/teams.py:285
msgid "Invite Link"
msgstr "رابط الدعوة"

#: CTFd/forms/users.py:133
msgid "Bracket"
msgstr ""

#: CTFd/forms/users.py:134
msgid "Competition bracket for your user"
msgstr ""

#: CTFd/forms/users.py:172
msgid "Search for matching users"
msgstr "البحث عن مستخدمين"

#: CTFd/themes/core-beta/templates/base.html:53
msgid "Powered by CTFd"
msgstr "مبني على CTFd"

#: CTFd/themes/core-beta/templates/challenge.html:11
#: CTFd/themes/core-beta/templates/teams/private.html:388
#: CTFd/themes/core-beta/templates/teams/public.html:157
#: CTFd/themes/core-beta/templates/users/private.html:124
#: CTFd/themes/core-beta/templates/users/public.html:123
msgid "Challenge"
msgstr "تحدي"

#: CTFd/themes/core-beta/templates/challenge.html:19
#, python-format
msgid "%(num)d Solve"
msgid_plural "%(num)d Solves"
msgstr[0] "%(num)d حل"
msgstr[1] "%(num)d حل"
msgstr[2] "%(num)d حل"
msgstr[3] "%(num)d حل"
msgstr[4] "%(num)d حل"
msgstr[5] "%(num)d حل"

#: CTFd/themes/core-beta/templates/challenge.html:77
msgid "View Hint"
msgstr "مشاهدة التلميحة"

#: CTFd/themes/core-beta/templates/challenge.html:120
msgid "attempt"
msgid_plural "attempts"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""
msgstr[4] ""
msgstr[5] ""

#: CTFd/themes/core-beta/templates/challenge.html:135
msgid "Flag"
msgstr "العلم"

#: CTFd/themes/core-beta/templates/challenge.html:169
msgid "Next Challenge"
msgstr "التحدي التالي"

#: CTFd/themes/core-beta/templates/challenge.html:175
msgid "Share"
msgstr ""

#: CTFd/themes/core-beta/templates/challenge.html:207
#: CTFd/themes/core-beta/templates/setup.html:305
#: CTFd/themes/core-beta/templates/setup.html:326
msgid "Date"
msgstr "التاريخ"

#: CTFd/themes/core-beta/templates/challenges.html:7
#: CTFd/themes/core-beta/templates/components/navbar.html:59
msgid "Challenges"
msgstr "التحديات"

#: CTFd/themes/core-beta/templates/confirm.html:7
msgid "Confirm"
msgstr "تأكيد"

#: CTFd/themes/core-beta/templates/confirm.html:18
msgid "We've sent a confirmation email to your email address."
msgstr "قمنا بإرسال رسالة تأكيد لبريدك الإلكتروني."

#: CTFd/themes/core-beta/templates/confirm.html:24
msgid "Please click the link in that email to confirm your account."
msgstr "افتح الرابط المرسل لك عبر البريد الإلكتروني لتفعيل الحساب."

#: CTFd/themes/core-beta/templates/confirm.html:30
msgid "If the email doesn’t arrive, check your spam folder or contact an administrator to manually verify your account."
msgstr "إذا لم يصلك البريد الإلكتروني، تأكد من مجلد البريد المهمل أو تواصل مع المسؤول ليقوم بتفعيل حسابك بشكل يدوي."

#: CTFd/themes/core-beta/templates/confirm.html:43
msgid "Change Email Address"
msgstr "تغير البريد الإلكتروني"

#: CTFd/themes/core-beta/templates/components/navbar.html:164
#: CTFd/themes/core-beta/templates/components/navbar.html:169
#: CTFd/themes/core-beta/templates/login.html:7
msgid "Login"
msgstr "تسجيل الدخول"

#: CTFd/themes/core-beta/templates/login.html:40
msgid "Forgot your password?"
msgstr "نسيت كلمة المرور؟"

#: CTFd/themes/core-beta/templates/components/navbar.html:85
#: CTFd/themes/core-beta/templates/components/navbar.html:91
#: CTFd/themes/core-beta/templates/notifications.html:7
msgid "Notifications"
msgstr "التنبيهات"

#: CTFd/themes/core-beta/templates/notifications.html:14
msgid "There are no notifications yet"
msgstr "لا توجد تنبيهات"

#: CTFd/themes/core-beta/templates/components/navbar.html:151
#: CTFd/themes/core-beta/templates/components/navbar.html:156
#: CTFd/themes/core-beta/templates/register.html:7
msgid "Register"
msgstr "تسجيل"

#: CTFd/themes/core-beta/templates/register.html:35
msgid "Your username on the site"
msgstr "اسم مستخدمك على الموقع"

#: CTFd/themes/core-beta/templates/register.html:43
msgid "Never shown to the public"
msgstr "لا يتم عرضها للعامة"

#: CTFd/themes/core-beta/templates/register.html:51
msgid "Password used to log into your account"
msgstr "كلمة المرور لتسجيل الدخول لحسابك"

#: CTFd/themes/core-beta/templates/register.html:69
#, python-format
msgid "By registering, you agree to the <a href=\"%(privacy_link)s\" target=\"_blank\">privacy policy</a> and <a href=\"%(tos_link)s\" target=\"_blank\">terms of service</a>"
msgstr ""

#: CTFd/themes/core-beta/templates/reset_password.html:7
msgid "Reset Password"
msgstr "إعادة ضبط كلمة المرور"

#: CTFd/themes/core-beta/templates/reset_password.html:21
msgid "You can now reset the password for your account and log in. Please enter in a new password below."
msgstr "تستطيع الآن إعادة ضبط كلمة المرور لحسابك وتسجيل الدخول. أدخل كلمة المرور الجديدة في الأسفل."

#: CTFd/themes/core-beta/templates/reset_password.html:44
msgid "Please provide the email address associated with your account below."
msgstr "قم بإدخال بريدك الإلكتروني الخاص بحسابك في الأسفل."

#: CTFd/themes/core-beta/templates/components/navbar.html:52
#: CTFd/themes/core-beta/templates/scoreboard.html:7
msgid "Scoreboard"
msgstr "لوحة النتائج"

#: CTFd/themes/core-beta/templates/scoreboard.html:24
msgid "All"
msgstr ""

#: CTFd/themes/core-beta/templates/scoreboard.html:36
msgid "Place"
msgstr ""

#: CTFd/themes/core-beta/templates/scoreboard.html:38
#: CTFd/themes/core-beta/templates/teams/private.html:330
#: CTFd/themes/core-beta/templates/teams/public.html:96
msgid "Score"
msgstr "نتيجة"

#: CTFd/themes/core-beta/templates/scoreboard.html:60
msgid "Scoreboard is empty"
msgstr ""

#: CTFd/themes/core-beta/templates/components/navbar.html:124
#: CTFd/themes/core-beta/templates/components/navbar.html:129
#: CTFd/themes/core-beta/templates/settings.html:8
#: CTFd/themes/core-beta/templates/setup.html:50
msgid "Settings"
msgstr "الإعدادات"

#: CTFd/themes/core-beta/templates/components/navbar.html:112
#: CTFd/themes/core-beta/templates/components/navbar.html:117
#: CTFd/themes/core-beta/templates/settings.html:21
msgid "Profile"
msgstr "الحساب"

#: CTFd/themes/core-beta/templates/settings.html:26
msgid "Access Tokens"
msgstr "رموز الوصول"

#: CTFd/themes/core-beta/templates/settings.html:95
msgid "Your profile has been updated"
msgstr "تم تحديث حسابك"

#: CTFd/themes/core-beta/templates/settings.html:103
#: CTFd/themes/core-beta/templates/teams/private.html:83
#: CTFd/themes/core-beta/templates/teams/private.html:198
msgid "Error:"
msgstr "خطأ:"

#: CTFd/themes/core-beta/templates/settings.html:129
msgid "API Key Generated"
msgstr "تم إنشاء مفتاح API"

#: CTFd/themes/core-beta/templates/settings.html:137
msgid "Please copy your API Key, it won't be shown again!"
msgstr "يرجى نسخ مفتاح API، لن يظهر مرة أخرى!"

#: CTFd/themes/core-beta/templates/settings.html:183
msgid "Active Tokens"
msgstr "الرموز الفعالة"

#: CTFd/themes/core-beta/templates/settings.html:195
msgid "Delete Token"
msgstr "حذف الرمز"

#: CTFd/themes/core-beta/templates/settings.html:204
msgid "Are you sure you want to delete this token?"
msgstr "هل أنت متأكد أنك تريد حذف الرمز؟"

#: CTFd/themes/core-beta/templates/settings.html:224
msgid "Created"
msgstr "أُنشئ"

#: CTFd/themes/core-beta/templates/settings.html:226
msgid "Description"
msgstr "وصف"

#: CTFd/themes/core-beta/templates/settings.html:227
msgid "Delete"
msgstr "حذف"

#: CTFd/themes/core-beta/templates/setup.html:24
msgid "Setup"
msgstr "الإعداد"

#: CTFd/themes/core-beta/templates/setup.html:44
msgid "General"
msgstr "عام"

#: CTFd/themes/core-beta/templates/setup.html:47
msgid "Mode"
msgstr "الوضع"

#: CTFd/themes/core-beta/templates/setup.html:53
msgid "Administration"
msgstr "الإدارة"

#: CTFd/themes/core-beta/templates/setup.html:56
msgid "Style"
msgstr "التنسيق"

#: CTFd/themes/core-beta/templates/setup.html:59
msgid "Date &amp; Time"
msgstr "التاريخ &amp; الوقت"

#: CTFd/themes/core-beta/templates/setup.html:62
msgid "Integrations"
msgstr "الربط"

#: CTFd/themes/core-beta/templates/setup.html:86
#: CTFd/themes/core-beta/templates/setup.html:138
#: CTFd/themes/core-beta/templates/setup.html:202
#: CTFd/themes/core-beta/templates/setup.html:238
#: CTFd/themes/core-beta/templates/setup.html:296
#: CTFd/themes/core-beta/templates/setup.html:345
msgid "Next"
msgstr "التالي"

#: CTFd/themes/core-beta/templates/setup.html:112
msgid "Participants register accounts and form teams"
msgstr "المشاركون يسجلون حسابات ليكونوا أفرقة"

#: CTFd/themes/core-beta/templates/setup.html:113
msgid "If a team member solves a challenge, the entire team receives credit"
msgstr "إذا حل عضو فريق أحد التحديات، يتم احتساب النقاط لجميع أعضاء الفريق"

#: CTFd/themes/core-beta/templates/setup.html:115
msgid "Easier to see which team member solved a challenge"
msgstr "يمكنك من رؤية أي أعضاء الفريق قام بحل التحدي"

#: CTFd/themes/core-beta/templates/setup.html:116
msgid "May be slightly more difficult to administer"
msgstr "أصعب تنظيميًا"

#: CTFd/themes/core-beta/templates/setup.html:120
msgid "Participants only register an individual account"
msgstr "المشاركون يسجلون حسابات فردية فقط"

#: CTFd/themes/core-beta/templates/setup.html:121
msgid "Players can share accounts to form pseudo-teams"
msgstr "اللاعبون يستطيعون مشاركة الحسابات لتكوين شبه-فريق"

#: CTFd/themes/core-beta/templates/setup.html:123
msgid "Easier to organize"
msgstr "أسهل تنظيمًا"

#: CTFd/themes/core-beta/templates/setup.html:124
msgid "Difficult to attribute solutions to individual team members"
msgstr "لن تستطيع تحديد من قام بحل التحدي من أعضاء الفريق"

#: CTFd/themes/core-beta/templates/setup.html:143
msgid "Visibility Settings"
msgstr "إعدادات الرؤية"

#: CTFd/themes/core-beta/templates/setup.html:146
msgid "Control the visibility of different sections of CTFd"
msgstr "التحكم في رؤية الأقسام المختلفة لـ CTFd"

#: CTFd/themes/core-beta/templates/setup.html:232
msgid "Subscribe email address to the CTFd LLC Newsletter for news and updates"
msgstr "سجل بريدك الإلكتروني في نشرة CTFd LLC البريدية لتصلك الأخبار والتحديثات"

#: CTFd/themes/core-beta/templates/setup.html:309
#: CTFd/themes/core-beta/templates/setup.html:330
#: CTFd/themes/core-beta/templates/teams/private.html:391
#: CTFd/themes/core-beta/templates/teams/public.html:160
#: CTFd/themes/core-beta/templates/users/private.html:127
#: CTFd/themes/core-beta/templates/users/public.html:126
msgid "Time"
msgstr "الوقت"

#: CTFd/themes/core-beta/templates/setup.html:334
msgid "UTC Preview"
msgstr "التوقيت العالمي الموحد"

#: CTFd/themes/core-beta/templates/components/navbar.html:36
#: CTFd/themes/core-beta/templates/users/users.html:6
msgid "Users"
msgstr "المستخدمين"

#: CTFd/themes/core-beta/templates/components/navbar.html:43
#: CTFd/themes/core-beta/templates/teams/teams.html:6
msgid "Teams"
msgstr "الفرق"

#: CTFd/themes/core-beta/templates/components/navbar.html:72
#: CTFd/themes/core-beta/templates/components/navbar.html:77
msgid "Admin Panel"
msgstr "صفحة المسؤول"

#: CTFd/themes/core-beta/templates/components/navbar.html:99
#: CTFd/themes/core-beta/templates/components/navbar.html:104
#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:6
#: CTFd/themes/core-beta/templates/teams/teams.html:49
msgid "Team"
msgstr "الفريق"

#: CTFd/themes/core-beta/templates/components/navbar.html:136
#: CTFd/themes/core-beta/templates/components/navbar.html:141
msgid "Logout"
msgstr "تسجيل خروج"

#: CTFd/themes/core-beta/templates/components/navbar.html:180
msgid "Change language"
msgstr ""

#: CTFd/themes/core-beta/templates/components/navbar.html:199
#: CTFd/themes/core-beta/templates/components/navbar.html:204
msgid "Toggle theme"
msgstr ""

#: CTFd/themes/core-beta/templates/errors/403.html:12
msgid "403 Forbidden"
msgstr ""

#: CTFd/themes/core-beta/templates/errors/404.html:9
msgid "File not found"
msgstr "الملف مفقود"

#: CTFd/themes/core-beta/templates/errors/404.html:12
msgid "404 Not Found"
msgstr ""

#: CTFd/themes/core-beta/templates/errors/429.html:9
msgid "Too many requests"
msgstr "طلبات كثيرة"

#: CTFd/themes/core-beta/templates/errors/429.html:10
msgid "Please slow down!"
msgstr "على مهلك!"

#: CTFd/themes/core-beta/templates/errors/429.html:13
msgid "429 Too Many Requests"
msgstr ""

#: CTFd/themes/core-beta/templates/errors/502.html:12
msgid "502 Bad Gateway"
msgstr ""

#: CTFd/themes/core-beta/templates/macros/forms.html:13
#: CTFd/themes/core-beta/templates/macros/forms.html:36
#: CTFd/themes/core-beta/templates/macros/forms.html:65
msgid "(Optional)"
msgstr "(اختياري)"

#: CTFd/themes/core-beta/templates/teams/invite.html:6
#: CTFd/themes/core-beta/templates/teams/join_team.html:6
#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:36
msgid "Join Team"
msgstr "انضم لفريق"

#: CTFd/themes/core-beta/templates/teams/invite.html:15
#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:12
msgid "Welcome to"
msgstr "مرحبًا بك في"

#: CTFd/themes/core-beta/templates/teams/invite.html:19
msgid "Click the button below to join the team!"
msgstr "اضغط الزر أدناه للانضمام للفريق!"

#: CTFd/themes/core-beta/templates/teams/new_team.html:6
#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:39
msgid "Create Team"
msgstr "إنشاء فريق"

#: CTFd/themes/core-beta/templates/teams/private.html:8
msgid "Edit Team"
msgstr "تحرير الفريق"

#: CTFd/themes/core-beta/templates/teams/private.html:76
msgid "Your team's profile has been updated"
msgstr "تم تحديث الملف الشخصي لفريقك"

#: CTFd/themes/core-beta/templates/teams/private.html:104
msgid "Choose Captain"
msgstr "اختر الكابتن"

#: CTFd/themes/core-beta/templates/teams/private.html:127
msgid "Your captain rights have been transferred"
msgstr "لقد تم نقل حقوق الكابتن الخاصة بك"

#: CTFd/themes/core-beta/templates/teams/private.html:152
msgid "Invite Users"
msgstr "دعوة المستخدمين"

#: CTFd/themes/core-beta/templates/teams/private.html:171
msgid "Share this link with your team members for them to join your team"
msgstr "شارك هذا الرابط مع أعضاء فريقك حتى ينضموا إلى فريقك"

#: CTFd/themes/core-beta/templates/teams/private.html:174
msgid "Invite links can be re-used and expire after 1 day"
msgstr "يمكن إعادة استخدام روابط الدعوة وتنتهي صلاحيتها بعد يوم واحد"

#: CTFd/themes/core-beta/templates/teams/private.html:188
msgid "Disband Team"
msgstr "حل الفريق"

#: CTFd/themes/core-beta/templates/teams/private.html:193
msgid "Are you sure you want to disband your team?"
msgstr "هل أنت متأكد أنك تريد حل فريقك؟"

#: CTFd/themes/core-beta/templates/teams/private.html:206
msgid "No"
msgstr "لا"

#: CTFd/themes/core-beta/templates/teams/private.html:207
msgid "Yes"
msgstr "نعم"

#: CTFd/themes/core-beta/templates/teams/private.html:218
#: CTFd/themes/core-beta/templates/teams/public.html:10
#: CTFd/themes/core-beta/templates/users/private.html:21
#: CTFd/themes/core-beta/templates/users/public.html:21
#: CTFd/themes/core-beta/templates/users/users.html:70
msgid "Official"
msgstr "رسمي"

#: CTFd/themes/core-beta/templates/teams/private.html:260
msgid "place"
msgstr "مكان"

#: CTFd/themes/core-beta/templates/teams/private.html:268
msgid "points"
msgstr "نقاط"

#: CTFd/themes/core-beta/templates/teams/private.html:325
#: CTFd/themes/core-beta/templates/teams/public.html:91
msgid "Members"
msgstr "أعضاء"

#: CTFd/themes/core-beta/templates/teams/private.html:345
#: CTFd/themes/core-beta/templates/teams/public.html:110
msgid "Captain"
msgstr "قائد المنتخب"

#: CTFd/themes/core-beta/templates/teams/private.html:361
#: CTFd/themes/core-beta/templates/teams/public.html:125
#: CTFd/themes/core-beta/templates/users/private.html:98
#: CTFd/themes/core-beta/templates/users/public.html:98
msgid "Awards"
msgstr "الجوائز"

#: CTFd/themes/core-beta/templates/teams/private.html:384
#: CTFd/themes/core-beta/templates/teams/public.html:153
#: CTFd/themes/core-beta/templates/users/private.html:120
msgid "Solves"
msgstr "الحلول"

#: CTFd/themes/core-beta/templates/teams/private.html:389
#: CTFd/themes/core-beta/templates/teams/public.html:158
#: CTFd/themes/core-beta/templates/users/private.html:125
#: CTFd/themes/core-beta/templates/users/public.html:124
msgid "Category"
msgstr "التصنيف"

#: CTFd/themes/core-beta/templates/teams/private.html:390
#: CTFd/themes/core-beta/templates/teams/public.html:159
#: CTFd/themes/core-beta/templates/users/private.html:126
#: CTFd/themes/core-beta/templates/users/public.html:125
msgid "Value"
msgstr "القيمة"

#: CTFd/themes/core-beta/templates/teams/private.html:481
#: CTFd/themes/core-beta/templates/teams/public.html:248
#: CTFd/themes/core-beta/templates/users/private.html:213
#: CTFd/themes/core-beta/templates/users/public.html:214
msgid "No solves yet"
msgstr "لا توجد حلول"

#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:14
msgid "In order to participate you must either join or create a team."
msgstr "للمشاركة يجب عليك الانضمام لفريق أو إنشاء فريق جديد."

#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:22
msgid "Play with Official Team"
msgstr "اللعب مع فريق رسمي"

#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:27
msgid "Join Unofficial Team"
msgstr "الانضمام لفريق غير رسمي"

#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:30
msgid "Create Unofficial Team"
msgstr "إنشاء فريق غير رسمي"

#: CTFd/themes/core-beta/templates/teams/teams.html:128
#: CTFd/themes/core-beta/templates/users/users.html:118
msgid "Page"
msgstr "صفحة"

#: CTFd/themes/core-beta/templates/users/users.html:14
#, python-format
msgid "Searching for users with <strong>%(field)s</strong> matching <strong>%(q)s</strong>"
msgstr ""

#: CTFd/themes/core-beta/templates/users/users.html:17
#, python-format
msgid "Page %(page)s of %(total)s results"
msgstr ""

#: CTFd/themes/core-beta/templates/users/users.html:46
msgid "User"
msgstr "مستخدم"

#: CTFd/utils/modes/__init__.py:35
msgid "user"
msgid_plural "users"
msgstr[0] "مستخدم"
msgstr[1] "مستخدم"
msgstr[2] "مستخدمان"
msgstr[3] "مستخدم"
msgstr[4] "مستخدم"
msgstr[5] "مستخدم"

#: CTFd/utils/modes/__init__.py:37
msgid "team"
msgid_plural "teams"
msgstr[0] "فريق"
msgstr[1] "فريق"
msgstr[2] "فريقان"
msgstr[3] "فرق"
msgstr[4] "فريق"
msgstr[5] "فريق"


<div role="tabpanel" class="tab-pane config-section" id="social">
	{% set social_shares = "false" if social_shares == False else "true" %}
	{% set social_share_solve_template = social_share_solve_template | default(Forms.config.SocialSettingsForm.social_share_solve_template.kwargs['default']) %}
	{% with form = Forms.config.SocialSettingsForm(social_shares=social_shares, social_share_solve_template=social_share_solve_template) %}
	<form method="POST" autocomplete="off" class="w-100">
		<div class="form-group">
			{{ form.social_shares.label }}
			{{ form.social_shares(class="form-control", value=social_shares) }}
			<small class="form-text text-muted">
				{{ form.social_shares.description }}
			</small>
		</div>
		<div class="form-group">
			{{ form.social_share_solve_template.label }}
			{{ form.social_share_solve_template(class="form-control", rows="10") }}
			<small class="form-text text-muted">
				{{ form.social_share_solve_template.description }}
			</small>
		</div>
		{{ form.submit(class="btn btn-md btn-primary float-right") }}
	</form>
	{% endwith %}
</div>

{% extends "admin/base.html" %}

{% block stylesheets %}
	{{ Assets.css("assets/css/challenge-board.scss", theme="admin") }}
{% endblock %}

{% block content %}
	<div class="modal fade" id="challenge-modal" role="dialog">
		<div class="modal-dialog modal-xl">
			<div class="modal-content">
				<div class="modal-header">
					<h3 class="text-center w-100 p-0 m-0">Challenge Preview</h3>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body" id="challenge-window" style="height: 80vh;">
				</div>
				<div class="text-center">
					<h5>
						Preview may be slightly different from the user-facing interface
					</h5>
				</div>
			</div>
		</div>
	</div>

	<div class="modal fade" id="challenge-ratings-window" role="dialog">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header">
					<h2 class="modal-title text-center w-100">Ratings</h2>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div class="row">
						<div class="col-md-12">
							<div id="ratings-box">
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="modal fade" id="challenge-comments-window" role="dialog">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header">
					<h2 class="modal-title text-center w-100">Comments</h2>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div class="row">
						<div class="col-md-12">
							<div id="comment-box">
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="jumbotron">
		<div class="container">
			<h1 class="text-center">{{ challenge.name }}</h1>
			<h2 class="text-center">{{ challenge.category }}</h2>
			<h2 class="text-center">{{ challenge.type }}</h2>
			{% set badge_state = 'badge-danger' if challenge.state == 'hidden' else 'badge-success' %}
			<h5>
				<span class="badge {{ badge_state }} challenge-state">
					{{ challenge.state }}
				</span>
			</h5>
			<h3 class="text-center">{{ challenge.value }} points</h3>
			<div class="pt-3">
				<a class="preview-challenge">
					<i class="btn-fa fas fa-file-alt fa-2x px-2" data-toggle="tooltip" data-placement="top"
					   title="Preview Challenge"></i>
				</a>
				<a class="no-decoration" href="{{ url_for('admin.submissions_listing', submission_type='correct', field='challenge_id', q=challenge.id) }}">
					<i class="btn-fa fas fa-tasks fa-2x px-2" data-toggle="tooltip" data-placement="top"
					   title="Correct Submissions"></i>
				</a>
				<a class="ratings-challenge" id="ratings-viewer-load">
					<i class="btn-fa fa-regular fa-star fa-2x px-2" data-toggle="tooltip" data-placement="top"
					   title="Challenge Ratings"></i>
				</a>
				<a class="comments-challenge">
					<i class="btn-fa fas fa-comments fa-2x px-2" data-toggle="tooltip" data-placement="top"
					   title="Comments"></i>
				</a>
				<a class="delete-challenge">
					<i class="btn-fa fas fa-trash-alt fa-2x px-2" data-toggle="tooltip" data-placement="top"
					   title="Delete Challenge"></i>
				</a>
			</div>
		</div>
	</div>

	<div class="container-fluid" style="width: 80%;">
		<div class="row">
			<div class="col-md-6">
				<nav class="nav nav-tabs nav-fill" id="challenge-properties" role="tablist">
					<a class="nav-item nav-link small active" data-toggle="tab" href="#files" role="tab">Files</a>
					<a class="nav-item nav-link small" data-toggle="tab" href="#flags" role="tab">Flags</a>
					<a class="nav-item nav-link small" data-toggle="tab" href="#solution" role="tab">Solution</a>
					<a class="nav-item nav-link small" data-toggle="tab" href="#topics" role="tab">Topics</a>
					<a class="nav-item nav-link small" data-toggle="tab" href="#tags" role="tab">Tags</a>
					<a class="nav-item nav-link small" data-toggle="tab" href="#hints" role="tab">Hints</a>
					<a class="nav-item nav-link small" data-toggle="tab" href="#requirements" role="tab">Requirements</a>
					<a class="nav-item nav-link small" data-toggle="tab" href="#next" role="tab">Next</a>
				</nav>

				<div class="tab-content" id="nav-tabContent">
					<div class="tab-pane fade show active" id="files" role="tabpanel">
						<div class="row">
							<div class="col-md-12">
								<h3 class="text-center py-3 d-block">Files</h3>
								{% include "admin/modals/challenges/files.html" %}
							</div>
						</div>
					</div>
					<div class="tab-pane fade" id="flags" role="tabpanel">
						<div class="row">
							<div class="col-md-12">
								<h3 class="text-center py-3 d-block">Flags</h3>
								{% include "admin/modals/challenges/flags.html" %}
							</div>
						</div>
					</div>
					<div class="tab-pane fade" id="solution" role="tabpanel">
						<div class="row">
							<div class="col-md-12">
								<h3 class="text-center py-3 d-block">Solution</h3>
								{% include "admin/modals/challenges/solution.html" %}
							</div>
						</div>
					</div>
					<div class="tab-pane fade" id="topics" role="tabpanel">
						<div class="row">
							<div class="col-md-12">
								<h3 class="text-center py-3 d-block">Topics</h3>
								{% include "admin/modals/challenges/topics.html" %}
							</div>
						</div>
					</div>
					<div class="tab-pane fade" id="tags" role="tabpanel">
						<div class="row">
							<div class="col-md-12">
								<h3 class="text-center py-3 d-block">Tags</h3>
								{% include "admin/modals/challenges/tags.html" %}
							</div>
						</div>
					</div>
					<div class="tab-pane fade" id="hints" role="tabpanel">
						<div class="row">
							<div class="col-md-12">
								<h3 class="text-center py-3 d-block">Hints</h3>
								{% include "admin/modals/challenges/hints.html" %}
							</div>
						</div>
					</div>
					<div class="tab-pane fade" id="requirements" role="tabpanel">
						<div class="row">
							<div class="col-md-12">
								<h3 class="text-center py-3 d-block">Requirements</h3>
								{% include "admin/modals/challenges/requirements.html" %}
							</div>
						</div>
					</div>
					<div class="tab-pane fade" id="next" role="tabpanel">
						<div class="row">
							<div class="col-md-12">
								<h3 class="text-center py-3 d-block">Next Challenge</h3>
								{% include "admin/modals/challenges/next.html" %}
							</div>
						</div>
					</div>
				</div>

			</div>
			<div id="challenge-update-container" class="col-md-6">
				{{ update_template | safe }}
			</div>
		</div>
	</div>

{% endblock %}

{% block scripts %}
	<script>
        var CHALLENGE_ID = {{ challenge.id }};
        var CHALLENGE_NAME = {{ challenge.name | tojson }};
        var CHALLENGE_SOLUTION_ID = {{ challenge.solution_id | tojson }};
	</script>
	<script defer src="{{ request.script_root }}{{ update_script }}"></script>
{% endblock %}


{% block entrypoint %}
	{{ Assets.js("assets/js/pages/challenge.js", theme="admin") }}
{% endblock %}

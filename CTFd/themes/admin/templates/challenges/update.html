{% block header %}
{% endblock %}

<form method="POST">
	{% block name %}
	<div class="form-group">
		<label>
			Name<br>
			<small class="form-text text-muted">Challenge Name</small>
		</label>
		<input type="text" class="form-control chal-name" name="name" value="{{ challenge.name }}">
	</div>
	{% endblock %}

	{% block category %}
	<div class="form-group">
		<label>
			Category<br>
			<small class="form-text text-muted">Challenge Category</small>
		</label>
		<input type="text" class="form-control chal-category" name="category" value="{{ challenge.category }}">
	</div>
	{% endblock %}

	{% block message %}
	<div class="form-group">
		<label>
			Message<br>
			<small class="form-text text-muted">
				Use this to give a brief introduction to your challenge.
			</small>
		</label>
		<textarea id="desc-editor" class="form-control chal-desc-editor markdown" name="description" rows="10">{{ challenge.description }}</textarea>
	</div>
	{% endblock %}

	{% block attribution %}
	<div class="form-group">
		<label>
			Attribution:<br>
			<small class="form-text text-muted">
				Attribution for your challenge <small>(supports markdown)</small>
			</small>
		</label>
		<input type="text" class="form-control" name="attribution" value="{{ challenge.attribution }}">
	</div>
	{% endblock %}

	{% block connection_info %}
	<div class="form-group">
		<label>
			Connection Info<br>
			<small class="form-text text-muted">
				Use this to specify a link, hostname, or connection instructions for your challenge.
			</small>
		</label>
		<input type="text" class="form-control chal-connection-info" name="connection_info" value="{{ challenge.connection_info | default('', true) }}">
	</div>
	{% endblock %}

	{% block value %}
	<div class="form-group">
		<label for="value">
			Value<br>
			<small class="form-text text-muted">
				This is how many points teams will receive once they solve this challenge.
			</small>
		</label>
		<input type="number" class="form-control chal-value" name="value" value="{{ challenge.value }}" required>
	</div>
	{% endblock %}

	{% block function %}
	<div class="form-group">
		<label for="function">
			Scoring Function<br>
			<small class="form-text text-muted">
				<span>How the challenge value will be calculated based on the Decay value</span>
			</small>
		</label>
		<select name="function" class="custom-select chal-function">
			<option value="static" {% if challenge.function == "static" %}selected{% endif %}>Static</option>
			<option value="linear" {% if challenge.function == "linear" %}selected{% endif %}>Linear</option>
			<option value="logarithmic" {% if challenge.function == "logarithmic" %}selected{% endif %}>Logarithmic</option>
		</select>
		<small class="form-text text-muted">
			<ul>
				<li>Static: Challenge <code>Value</code> is awarded as-is</li>
				<li>Linear: Calculated as <code>Initial - (Decay * SolveCount)</code></li>
				<li>Logarithmic: Calculated as <code>(((Minimum - Initial) / (Decay^2)) * (SolveCount^2)) + Initial</code></li>
			</ul>
		</small>
	</div>
	{% endblock %}

	{% block initial %}
	<div class="form-group">
		<label for="initial">Initial Value<br>
			<small class="form-text text-muted">
				This is how many points the challenge was worth initially.
			</small>
		</label>
		<input type="number" class="form-control chal-initial" name="initial" value="{{ challenge.initial }}" required>
	</div>
	{% endblock %}

	{% block decay %}
	<div class="form-group">
		<label for="decay">Decay<br>
			<small class="form-text text-muted">
				<span>The decay value is used differently depending on the above Decay Function</span>
				<ul>
					<li>Linear: The amount of points deducted per solve. Equal deduction per solve.</li>
					<li>Logarithmic: The amount of solves before the challenge reaches its minimum value. Earlier solves will lose less points. Later solves will lose more points</li>
				</ul>
			</small>
		</label>
		<input type="number" class="form-control chal-decay" min="1" name="decay" value="{{ challenge.decay }}" required>
	</div>
	{% endblock %}

	{% block minimum %}
	<div class="form-group">
		<label for="minimum">Minimum Value<br>
			<small class="form-text text-muted">
				This is the lowest that the challenge can be worth
			</small>
		</label>
		<input type="number" class="form-control chal-minimum" name="minimum" value="{{ challenge.minimum }}" required>
	</div>
	{% endblock %}

	{% block logic %}
	<div class="form-group">
		<label>
			Logic<br>
			<small class="form-text text-muted">Determines how this challenge is solved</small>
		</label>

		<select class="form-control custom-select" name="logic">
			<option value="any" {% if challenge.logic == "any" or not challenge.logic %}selected{% endif %}>Require Any Flag</option>
			<option value="all" {% if challenge.logic == "all" %}selected{% endif %}>Require All Flags</option>
			<option value="team" {% if challenge.logic == "team" %}selected{% endif %}>Require Any Flags by All Team Members</option>
		</select>
	</div>
	{% endblock %}

	{% block max_attempts %}
	<div class="form-group">
		<label>
			Max Attempts<br>
			<small class="form-text text-muted">Maximum amount of attempts users receive. Leave at 0 for unlimited.</small>
		</label>

		<input type="number" class="form-control chal-attempts" name="max_attempts" value="{{ challenge.max_attempts }}">
	</div>
	{% endblock %}

	{% block state %}
	<div class="form-group">
		<label>
			State<br>
			<small class="form-text text-muted">Changes the state of the challenge (e.g. visible, hidden)</small>
		</label>

		<select class="form-control custom-select" name="state">
			<option value="visible" {% if challenge.state == "visible" %}selected{% endif %}>Visible</option>
			<option value="hidden" {% if challenge.state == "hidden" %}selected{% endif %}>Hidden</option>
		</select>
	</div>
	{% endblock %}

	{% block submit %}
	<div>
		<button class="btn btn-success btn-outlined float-right" type="submit">
			Update
		</button>
	</div>
	{% endblock %}
</form>

{% block footer %}
{% endblock %}
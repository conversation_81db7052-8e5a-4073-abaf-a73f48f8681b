import{_ as u,C as h,y as p,s as m,L as _,c as l,a as e,h as g,l as b,t as i,b as c,f as v,g as f,T as C,o as d,F as y,r as k,p as x,k as $}from"./pages/main-CcyKUC_q.js";const w={props:{type:String,id:Number},data:function(){return{page:1,pages:null,next:null,prev:null,total:null,comment:"",comments:[],urlRoot:h.config.urlRoot}},methods:{toLocalTime(t){return p(t).format("MMMM Do, h:mm:ss A")},nextPage:function(){this.page++,this.loadComments()},prevPage:function(){this.page--,this.loadComments()},getArgs:function(){let t={};return t[`${this.$props.type}_id`]=this.$props.id,t},loadComments:function(){let t=this.getArgs();t.page=this.page,t.per_page=10,m.comments.get_comments(t).then(s=>(this.page=s.meta.pagination.page,this.pages=s.meta.pagination.pages,this.next=s.meta.pagination.next,this.prev=s.meta.pagination.prev,this.total=s.meta.pagination.total,this.comments=s.data,this.comments))},submitComment:function(){let t=this.comment.trim();t.length>0&&m.comments.add_comment(t,this.$props.type,this.getArgs(),()=>{this.loadComments()}),this.comment=""},deleteComment:function(t){confirm("Are you sure you'd like to delete this comment?")&&m.comments.delete_comment(t).then(s=>{if(s.success===!0)for(let a=this.comments.length-1;a>=0;--a)this.comments[a].id==t&&this.comments.splice(a,1)})}},created(){this.loadComments()},updated(){this.$el.querySelectorAll("pre code").forEach(t=>{_.highlightBlock(t)})}},r=t=>(x("data-v-1eb8ceda"),t=t(),$(),t),A={class:"row mb-3"},P={class:"col-md-12"},T={class:"comment"},M={key:0,class:"row"},S={class:"col-md-12"},L={class:"text-center"},B=["disabled"],N=["disabled"],V={class:"col-md-12"},j={class:"text-center"},D={class:"text-muted"},F={class:"comments"},H={class:"d-flex justify-content-between align-items-start mb-2"},I=r(()=>e("div",null,null,-1)),R=["onClick"],E=r(()=>e("span",{"aria-hidden":"true"},"×",-1)),q=[E],z={class:"comment-content"},G=["innerHTML"],J={class:"d-flex justify-content-between"},U={class:"text-muted"},K=["href"],O={class:"text-muted"},Q={key:1,class:"row"},W={class:"col-md-12"},X={class:"text-center"},Y=["disabled"],Z=["disabled"],tt={class:"col-md-12"},et={class:"text-center"},st={class:"text-muted"};function ot(t,s,a,nt,it,n){return d(),l("div",null,[e("div",A,[e("div",P,[e("div",T,[g(e("textarea",{class:"form-control mb-2",rows:"2",id:"comment-input",placeholder:"Add comment","onUpdate:modelValue":s[0]||(s[0]=o=>t.comment=o)},null,512),[[b,t.comment,void 0,{lazy:!0}]]),e("button",{class:"btn btn-sm btn-success btn-outlined float-right",type:"submit",onClick:s[1]||(s[1]=o=>n.submitComment())}," Comment ")])])]),t.pages>1?(d(),l("div",M,[e("div",S,[e("div",L,[e("button",{type:"button",class:"btn btn-link p-0",onClick:s[2]||(s[2]=o=>n.prevPage()),disabled:!t.prev}," <<< ",8,B),e("button",{type:"button",class:"btn btn-link p-0",onClick:s[3]||(s[3]=o=>n.nextPage()),disabled:!t.next}," >>> ",8,N)])]),e("div",V,[e("div",j,[e("small",D,"Page "+i(t.page)+" of "+i(t.total)+" comments",1)])])])):c("",!0),e("div",F,[v(C,{name:"comment-item"},{default:f(()=>[(d(!0),l(y,null,k(t.comments,o=>(d(),l("div",{class:"comment-item border rounded p-3 mb-2",key:o.id},[e("div",H,[I,e("button",{type:"button",class:"close","aria-label":"Close",onClick:at=>n.deleteComment(o.id)},q,8,R)]),e("div",z,[e("div",{class:"mb-2",innerHTML:o.html},null,8,G),e("div",J,[e("small",U,[e("a",{href:`${t.urlRoot}/admin/users/${o.author_id}`},i(o.author.name),9,K)]),e("small",O,i(n.toLocalTime(o.date)),1)])])]))),128))]),_:1})]),t.pages>1?(d(),l("div",Q,[e("div",W,[e("div",X,[e("button",{type:"button",class:"btn btn-link p-0",onClick:s[4]||(s[4]=o=>n.prevPage()),disabled:!t.prev}," <<< ",8,Y),e("button",{type:"button",class:"btn btn-link p-0",onClick:s[5]||(s[5]=o=>n.nextPage()),disabled:!t.next}," >>> ",8,Z)])]),e("div",tt,[e("div",et,[e("small",st,"Page "+i(t.page)+" of "+i(t.total)+" comments",1)])])])):c("",!0)])}const dt=u(w,[["render",ot],["__scopeId","data-v-1eb8ceda"]]);export{dt as C};

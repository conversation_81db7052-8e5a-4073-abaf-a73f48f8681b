import{_ as _export_sfc,$ as $$1,C as CTFd$1,n as nunjucks,o as openBlock,c as createElementBlock,a as createBaseVNode,F as Fragment,r as renderList,t as toDisplayString,w as withModifiers,b as createCommentVNode,d as createStaticVNode,e as resolveComponent,f as createVNode,g as withCtx,T as TransitionGroup,h as withDirectives,v as vModelSelect,i as vModelCheckbox,j as createTextVNode,p as pushScopeId,k as popScopeId,l as vModelText,m as withKeys,q as normalizeClass,s as helpers,u as ezQuery,x as bindMarkdownEditor,y as dayjs,z as htmlEntities,V as Vue$1,A as bindMarkdownEditors,B as ezAlert,D as ezToast}from"./main-CcyKUC_q.js";import"../tab-BrZ-GZoF.js";import{C as CommentBox}from"../CommentBox-CBClD8yz.js";const _sfc_main$c={name:"FlagCreationForm",props:{challenge_id:Number},data:function(){return{types:{},selectedType:null,createForm:""}},methods:{selectType:function(event){let flagType=event.target.value;if(this.types[flagType]===void 0){this.selectedType=null,this.createForm="";return}let createFormURL=this.types[flagType].templates.create;$$1.get(CTFd$1.config.urlRoot+createFormURL,template_data=>{const template=nunjucks.compile(template_data);this.selectedType=flagType,this.createForm=template.render(),this.createForm.includes("<script")&&setTimeout(()=>{$$1("<div>"+this.createForm+"</div>").find("script").each(function(){eval($$1(this).html())})},100)})},loadTypes:function(){CTFd$1.fetch("/api/v1/flags/types",{method:"GET"}).then(e=>e.json()).then(e=>{this.types=e.data})},submitFlag:function(e){let i=$$1(e.target).serializeJSON(!0);i.challenge=this.$props.challenge_id,CTFd$1.fetch("/api/v1/flags",{method:"POST",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(i)}).then(n=>n.json()).then(n=>{this.$emit("refreshFlags",this.$options.name)})}},created(){this.loadTypes()}},_hoisted_1$c={id:"flag-create-modal",class:"modal fade",tabindex:"-1"},_hoisted_2$c={class:"modal-dialog modal-lg"},_hoisted_3$c={class:"modal-content"},_hoisted_4$c=createStaticVNode('<div class="modal-header text-center"><div class="container"><div class="row"><div class="col-md-12"><h3>Create Flag</h3></div></div></div><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button></div>',1),_hoisted_5$c={class:"modal-body"},_hoisted_6$b={class:"create-keys-select-div"},_hoisted_7$9=createBaseVNode("label",{for:"create-keys-select",class:"control-label"}," Choose Flag Type ",-1),_hoisted_8$9=createBaseVNode("option",null,"--",-1),_hoisted_9$9=["value"],_hoisted_10$8=createBaseVNode("br",null,null,-1),_hoisted_11$6=["innerHTML"],_hoisted_12$5={key:0,class:"btn btn-success float-right",type:"submit"};function _sfc_render$c(e,t,i,n,a,o){return openBlock(),createElementBlock("div",_hoisted_1$c,[createBaseVNode("div",_hoisted_2$c,[createBaseVNode("div",_hoisted_3$c,[_hoisted_4$c,createBaseVNode("div",_hoisted_5$c,[createBaseVNode("div",_hoisted_6$b,[_hoisted_7$9,createBaseVNode("select",{class:"form-control custom-select",onChange:t[0]||(t[0]=s=>o.selectType(s))},[_hoisted_8$9,(openBlock(!0),createElementBlock(Fragment,null,renderList(Object.keys(e.types),s=>(openBlock(),createElementBlock("option",{value:s,key:s},toDisplayString(s),9,_hoisted_9$9))),128))],32)]),_hoisted_10$8,createBaseVNode("form",{onSubmit:t[1]||(t[1]=withModifiers((...s)=>o.submitFlag&&o.submitFlag(...s),["prevent"]))},[createBaseVNode("div",{id:"create-flag-form",innerHTML:e.createForm},null,8,_hoisted_11$6),e.createForm?(openBlock(),createElementBlock("button",_hoisted_12$5," Create Flag ")):createCommentVNode("",!0)],32)])])])])}const FlagCreationForm=_export_sfc(_sfc_main$c,[["render",_sfc_render$c]]),_sfc_main$b={name:"FlagEditForm",props:{flag_id:Number},data:function(){return{flag:{},editForm:""}},watch:{flag_id:{immediate:!0,handler(e,t){e!==null&&this.loadFlag()}}},methods:{loadFlag:function(){CTFd$1.fetch(`/api/v1/flags/${this.$props.flag_id}`,{method:"GET"}).then(e=>e.json()).then(response=>{this.flag=response.data;let editFormURL=this.flag.templates.update;$$1.get(CTFd$1.config.urlRoot+editFormURL,template_data=>{const template=nunjucks.compile(template_data);this.editForm=template.render(this.flag),this.editForm.includes("<script")&&setTimeout(()=>{$$1("<div>"+this.editForm+"</div>").find("script").each(function(){eval($$1(this).html())})},100)})})},updateFlag:function(e){let i=$$1(e.target).serializeJSON(!0);CTFd$1.fetch(`/api/v1/flags/${this.$props.flag_id}`,{method:"PATCH",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(i)}).then(n=>n.json()).then(n=>{this.$emit("refreshFlags",this.$options.name)})}},mounted(){this.flag_id&&this.loadFlag()},created(){this.flag_id&&this.loadFlag()}},_hoisted_1$b={id:"flag-edit-modal",class:"modal fade",tabindex:"-1"},_hoisted_2$b={class:"modal-dialog modal-lg"},_hoisted_3$b={class:"modal-content"},_hoisted_4$b=createStaticVNode('<div class="modal-header text-center"><div class="container"><div class="row"><div class="col-md-12"><h3 class="text-center">Edit Flag</h3></div></div></div><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button></div>',1),_hoisted_5$b={class:"modal-body"},_hoisted_6$a=["innerHTML"];function _sfc_render$b(e,t,i,n,a,o){return openBlock(),createElementBlock("div",_hoisted_1$b,[createBaseVNode("div",_hoisted_2$b,[createBaseVNode("div",_hoisted_3$b,[_hoisted_4$b,createBaseVNode("div",_hoisted_5$b,[createBaseVNode("form",{method:"POST",innerHTML:e.editForm,onSubmit:t[0]||(t[0]=withModifiers((...s)=>o.updateFlag&&o.updateFlag(...s),["prevent"]))},null,40,_hoisted_6$a)])])])])}const FlagEditForm=_export_sfc(_sfc_main$b,[["render",_sfc_render$b]]),_sfc_main$a={components:{FlagCreationForm,FlagEditForm},props:{challenge_id:Number},data:function(){return{flags:[],editing_flag_id:null}},methods:{loadFlags:function(){CTFd$1.fetch(`/api/v1/challenges/${this.$props.challenge_id}/flags`,{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(e=>e.json()).then(e=>{e.success&&(this.flags=e.data)})},refreshFlags(e){this.loadFlags();let t;switch(e){case"FlagEditForm":t=this.$refs.FlagEditForm.$el,$$1(t).modal("hide");break;case"FlagCreationForm":t=this.$refs.FlagCreationForm.$el,$$1(t).modal("hide");break}},addFlag:function(){let e=this.$refs.FlagCreationForm.$el;$$1(e).modal()},editFlag:function(e){this.editing_flag_id=e;let t=this.$refs.FlagEditForm.$el;$$1(t).modal()},deleteFlag:function(e){confirm("Are you sure you'd like to delete this flag?")&&CTFd$1.fetch(`/api/v1/flags/${e}`,{method:"DELETE"}).then(t=>t.json()).then(t=>{t.success&&this.loadFlags()})}},created(){this.loadFlags()}},_hoisted_1$a={id:"flagsboard",class:"table table-striped"},_hoisted_2$a=createBaseVNode("thead",null,[createBaseVNode("tr",null,[createBaseVNode("td",{class:"text-center"},[createBaseVNode("b",null,"Type")]),createBaseVNode("td",{class:"text-center"},[createBaseVNode("b",null,"Flag")]),createBaseVNode("td",{class:"text-center"},[createBaseVNode("b",null,"Settings")])])],-1),_hoisted_3$a=["name"],_hoisted_4$a={class:"text-center"},_hoisted_5$a={class:"text-break"},_hoisted_6$9={class:"flag-content"},_hoisted_7$8={class:"text-center"},_hoisted_8$8=["flag-id","flag-type","onClick"],_hoisted_9$8=["flag-id","onClick"],_hoisted_10$7={class:"col-md-12"};function _sfc_render$a(e,t,i,n,a,o){const s=resolveComponent("FlagCreationForm"),l=resolveComponent("FlagEditForm");return openBlock(),createElementBlock("div",null,[createBaseVNode("div",null,[createVNode(s,{ref:"FlagCreationForm",challenge_id:i.challenge_id,onRefreshFlags:o.refreshFlags},null,8,["challenge_id","onRefreshFlags"])]),createBaseVNode("div",null,[createVNode(l,{ref:"FlagEditForm",flag_id:e.editing_flag_id,onRefreshFlags:o.refreshFlags},null,8,["flag_id","onRefreshFlags"])]),createBaseVNode("table",_hoisted_1$a,[_hoisted_2$a,createBaseVNode("tbody",null,[(openBlock(!0),createElementBlock(Fragment,null,renderList(e.flags,d=>(openBlock(),createElementBlock("tr",{name:d.id,key:d.id},[createBaseVNode("td",_hoisted_4$a,toDisplayString(d.type),1),createBaseVNode("td",_hoisted_5$a,[createBaseVNode("pre",_hoisted_6$9,toDisplayString(d.content),1)]),createBaseVNode("td",_hoisted_7$8,[createBaseVNode("i",{role:"button",class:"btn-fa fas fa-edit edit-flag","flag-id":d.id,"flag-type":d.type,onClick:r=>o.editFlag(d.id)},null,8,_hoisted_8$8),createBaseVNode("i",{role:"button",class:"btn-fa fas fa-times delete-flag","flag-id":d.id,onClick:r=>o.deleteFlag(d.id)},null,8,_hoisted_9$8)])],8,_hoisted_3$a))),128))])]),createBaseVNode("div",_hoisted_10$7,[createBaseVNode("button",{id:"flag-add-button",class:"btn btn-success d-inline-block float-right",onClick:t[0]||(t[0]=d=>o.addFlag())}," Create Flag ")])])}const FlagList=_export_sfc(_sfc_main$a,[["render",_sfc_render$a]]),_sfc_main$9={props:{challenge_id:Number},data:function(){return{challenges:[],requirements:{},selectedRequirements:[],selectedAnonymize:!1}},computed:{newRequirements:function(){let e=this.requirements.prerequisites||[],t=this.requirements.anonymize||!1,i=JSON.stringify(e.sort())!==JSON.stringify(this.selectedRequirements.sort()),n=t!==this.selectedAnonymize;return i||n},requiredChallenges:function(){const e=this.requirements.prerequisites||[];return this.challenges.filter(t=>t.id!==this.$props.challenge_id&&e.includes(t.id))},otherChallenges:function(){const e=this.requirements.prerequisites||[];return this.challenges.filter(t=>t.id!==this.$props.challenge_id&&!e.includes(t.id))}},methods:{loadChallenges:function(){CTFd$1.fetch("/api/v1/challenges?view=admin",{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(e=>e.json()).then(e=>{e.success&&(this.challenges=e.data)})},getChallengeNameById:function(e){let t=this.challenges.find(i=>i.id===e);return t?t.name:""},loadRequirements:function(){CTFd$1.fetch(`/api/v1/challenges/${this.$props.challenge_id}/requirements`,{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(e=>e.json()).then(e=>{e.success&&(this.requirements=e.data||{},this.selectedRequirements=this.requirements.prerequisites||[],this.selectedAnonymize=this.requirements.anonymize||!1)})},updateRequirements:function(){const t={requirements:{prerequisites:this.selectedRequirements}};this.selectedAnonymize&&(t.requirements.anonymize=!0),CTFd$1.fetch(`/api/v1/challenges/${this.$props.challenge_id}`,{method:"PATCH",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(t)}).then(i=>i.json()).then(i=>{i.success&&this.loadRequirements()})}},created(){this.loadChallenges(),this.loadRequirements()}},_withScopeId=e=>(pushScopeId("data-v-a3fa86b4"),e=e(),popScopeId(),e),_hoisted_1$9={class:"form-group scrollbox"},_hoisted_2$9={class:"form-check-label cursor-pointer"},_hoisted_3$9=["value"],_hoisted_4$9={class:"form-check-label cursor-pointer"},_hoisted_5$9=["value"],_hoisted_6$8={class:"form-group"},_hoisted_7$7=_withScopeId(()=>createBaseVNode("label",null,[createBaseVNode("b",null,"Behavior if not unlocked")],-1)),_hoisted_8$7=_withScopeId(()=>createBaseVNode("option",{value:!1},"Hidden",-1)),_hoisted_9$7=_withScopeId(()=>createBaseVNode("option",{value:!0},"Anonymized",-1)),_hoisted_10$6=[_hoisted_8$7,_hoisted_9$7],_hoisted_11$5={class:"form-group"},_hoisted_12$4=["disabled"];function _sfc_render$9(e,t,i,n,a,o){return openBlock(),createElementBlock("div",null,[createBaseVNode("form",{onSubmit:t[3]||(t[3]=withModifiers((...s)=>o.updateRequirements&&o.updateRequirements(...s),["prevent"]))},[createBaseVNode("div",_hoisted_1$9,[createVNode(TransitionGroup,{name:"flip-list"},{default:withCtx(()=>[(openBlock(!0),createElementBlock(Fragment,null,renderList(o.requiredChallenges,s=>(openBlock(),createElementBlock("div",{class:"form-check",key:s.id},[createBaseVNode("label",_hoisted_2$9,[withDirectives(createBaseVNode("input",{class:"form-check-input",type:"checkbox",value:s.id,"onUpdate:modelValue":t[0]||(t[0]=l=>e.selectedRequirements=l)},null,8,_hoisted_3$9),[[vModelCheckbox,e.selectedRequirements]]),createTextVNode(" "+toDisplayString(s.name),1)])]))),128)),(openBlock(!0),createElementBlock(Fragment,null,renderList(o.otherChallenges,s=>(openBlock(),createElementBlock("div",{class:"form-check",key:s.id},[createBaseVNode("label",_hoisted_4$9,[withDirectives(createBaseVNode("input",{class:"form-check-input",type:"checkbox",value:s.id,"onUpdate:modelValue":t[1]||(t[1]=l=>e.selectedRequirements=l)},null,8,_hoisted_5$9),[[vModelCheckbox,e.selectedRequirements]]),createTextVNode(" "+toDisplayString(s.name),1)])]))),128))]),_:1})]),createBaseVNode("div",_hoisted_6$8,[_hoisted_7$7,withDirectives(createBaseVNode("select",{class:"form-control custom-select",name:"anonymize","onUpdate:modelValue":t[2]||(t[2]=s=>e.selectedAnonymize=s)},_hoisted_10$6,512),[[vModelSelect,e.selectedAnonymize]])]),createBaseVNode("div",_hoisted_11$5,[createBaseVNode("button",{class:"btn btn-success float-right",disabled:!o.newRequirements}," Save ",8,_hoisted_12$4)])],32)])}const Requirements=_export_sfc(_sfc_main$9,[["render",_sfc_render$9],["__scopeId","data-v-a3fa86b4"]]),_sfc_main$8={props:{challenge_id:Number},data:function(){return{topics:[],topicValue:"",searchedTopic:"",topicResults:[],selectedResultIdx:0,awaitingSearch:!1}},methods:{loadTopics:function(){CTFd$1.fetch(`/api/v1/challenges/${this.$props.challenge_id}/topics`,{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(e=>e.json()).then(e=>{e.success&&(this.topics=e.data)})},searchTopics:function(){if(this.selectedResultIdx=0,this.topicValue==""){this.topicResults=[];return}CTFd$1.fetch(`/api/v1/topics?field=value&q=${this.topicValue}`,{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(e=>e.json()).then(e=>{e.success&&(this.topicResults=e.data.slice(0,10))})},addTopic:function(){let e;if(this.selectedResultIdx===0)e=this.topicValue;else{let i=this.selectedResultIdx-1;e=this.topicResults[i].value}const t={value:e,challenge:this.$props.challenge_id,type:"challenge"};CTFd$1.fetch("/api/v1/topics",{method:"POST",body:JSON.stringify(t)}).then(i=>i.json()).then(i=>{i.success&&(this.topicValue="",this.loadTopics())})},deleteTopic:function(e){CTFd$1.fetch(`/api/v1/topics?type=challenge&target_id=${e}`,{method:"DELETE"}).then(t=>t.json()).then(t=>{t.success&&this.loadTopics()})},moveCursor:function(e){switch(e){case"up":this.selectedResultIdx&&(this.selectedResultIdx-=1);break;case"down":this.selectedResultIdx<this.topicResults.length&&(this.selectedResultIdx+=1);break}},selectTopic:function(e){e===void 0&&(e=this.selectedResultIdx);let t=this.topicResults[e];this.topicValue=t.value}},watch:{topicValue:function(e){this.awaitingSearch===!1&&setTimeout(()=>{this.searchTopics(),this.awaitingSearch=!1},500),this.awaitingSearch=!0}},created(){this.loadTopics()}},_hoisted_1$8={class:"col-md-12"},_hoisted_2$8={id:"challenge-topics",class:"my-3"},_hoisted_3$8={class:"mr-1"},_hoisted_4$8=["onClick"],_hoisted_5$8={class:"form-group"},_hoisted_6$7=createBaseVNode("label",null,[createTextVNode(" Topic "),createBaseVNode("br"),createBaseVNode("small",{class:"text-muted"},"Type topic and press Enter")],-1),_hoisted_7$6={class:"form-group"},_hoisted_8$6={class:"list-group"},_hoisted_9$6=["onClick"];function _sfc_render$8(e,t,i,n,a,o){return openBlock(),createElementBlock("div",_hoisted_1$8,[createBaseVNode("div",_hoisted_2$8,[(openBlock(!0),createElementBlock(Fragment,null,renderList(e.topics,s=>(openBlock(),createElementBlock("h5",{class:"challenge-tag",key:s.id},[createBaseVNode("span",_hoisted_3$8,toDisplayString(s.value),1),createBaseVNode("a",{class:"btn-fa delete-tag",onClick:l=>o.deleteTopic(s.id)}," ×",8,_hoisted_4$8)]))),128))]),createBaseVNode("div",_hoisted_5$8,[_hoisted_6$7,withDirectives(createBaseVNode("input",{id:"tags-add-input",maxlength:"255",type:"text",class:"form-control","onUpdate:modelValue":t[0]||(t[0]=s=>e.topicValue=s),onKeyup:[t[1]||(t[1]=withKeys(s=>o.moveCursor("down"),["down"])),t[2]||(t[2]=withKeys(s=>o.moveCursor("up"),["up"])),t[3]||(t[3]=withKeys(s=>o.addTopic(),["enter"]))]},null,544),[[vModelText,e.topicValue]])]),createBaseVNode("div",_hoisted_7$6,[createBaseVNode("ul",_hoisted_8$6,[(openBlock(!0),createElementBlock(Fragment,null,renderList(e.topicResults,(s,l)=>(openBlock(),createElementBlock("li",{class:normalizeClass({"list-group-item":!0,active:l+1===e.selectedResultIdx}),key:s.id,onClick:d=>o.selectTopic(l)},toDisplayString(s.value),11,_hoisted_9$6))),128))])])])}const TopicsList=_export_sfc(_sfc_main$8,[["render",_sfc_render$8]]),_sfc_main$7={props:{challenge_id:Number},data:function(){return{tags:[],tagValue:""}},methods:{loadTags:function(){CTFd$1.fetch(`/api/v1/challenges/${this.$props.challenge_id}/tags`,{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(e=>e.json()).then(e=>{e.success&&(this.tags=e.data)})},addTag:function(){if(this.tagValue){const e={value:this.tagValue,challenge:this.$props.challenge_id};CTFd$1.api.post_tag_list({},e).then(t=>{t.success&&(this.tagValue="",this.loadTags())})}},deleteTag:function(e){CTFd$1.api.delete_tag({tagId:e}).then(t=>{t.success&&this.loadTags()})}},created(){this.loadTags()}},_hoisted_1$7={class:"col-md-12"},_hoisted_2$7={id:"challenge-tags",class:"my-3"},_hoisted_3$7=["onClick"],_hoisted_4$7={class:"form-group"},_hoisted_5$7=createBaseVNode("label",null,[createTextVNode("Tag "),createBaseVNode("br"),createBaseVNode("small",{class:"text-muted"},"Type tag and press Enter")],-1);function _sfc_render$7(e,t,i,n,a,o){return openBlock(),createElementBlock("div",_hoisted_1$7,[createBaseVNode("div",_hoisted_2$7,[(openBlock(!0),createElementBlock(Fragment,null,renderList(e.tags,s=>(openBlock(),createElementBlock("span",{class:"badge badge-primary mx-1 challenge-tag",key:s.id},[createBaseVNode("span",null,toDisplayString(s.value),1),createBaseVNode("a",{class:"btn-fa delete-tag",onClick:l=>o.deleteTag(s.id)}," ×",8,_hoisted_3$7)]))),128))]),createBaseVNode("div",_hoisted_4$7,[_hoisted_5$7,withDirectives(createBaseVNode("input",{id:"tags-add-input",maxlength:"80",type:"text",class:"form-control","onUpdate:modelValue":t[0]||(t[0]=s=>e.tagValue=s),onKeyup:t[1]||(t[1]=withKeys(s=>o.addTag(),["enter"]))},null,544),[[vModelText,e.tagValue]])])])}const TagsList=_export_sfc(_sfc_main$7,[["render",_sfc_render$7]]),_sfc_main$6={props:{challenge_id:Number},data:function(){return{files:[],urlRoot:CTFd$1.config.urlRoot}},methods:{loadFiles:function(){CTFd$1.fetch(`/api/v1/challenges/${this.$props.challenge_id}/files`,{method:"GET"}).then(e=>e.json()).then(e=>{e.success&&(this.files=e.data)})},addFiles:function(){let e={challenge:this.$props.challenge_id,type:"challenge"},t=this.$refs.FileUploadForm;helpers.files.upload(t,e,i=>{setTimeout(()=>{this.loadFiles()},700)})},deleteFile:function(e){ezQuery({title:"Delete Files",body:"Are you sure you want to delete this file?",success:()=>{CTFd$1.fetch(`/api/v1/files/${e}`,{method:"DELETE"}).then(t=>t.json()).then(t=>{t.success&&this.loadFiles()})}})}},created(){this.loadFiles()}},_hoisted_1$6={id:"filesboard",class:"table table-striped"},_hoisted_2$6=createBaseVNode("thead",null,[createBaseVNode("tr",null,[createBaseVNode("td",{class:"text-center"},[createBaseVNode("b",null,"File")]),createBaseVNode("td",{class:"text-center"},[createBaseVNode("b",null,"Settings")])])],-1),_hoisted_3$6={class:"text-left"},_hoisted_4$6=["href"],_hoisted_5$6={class:"d-flex flex-row align-items-center"},_hoisted_6$6=createBaseVNode("strong",{class:"mr-2 small"}," SHA1: ",-1),_hoisted_7$5={class:"d-inline-block mr-2 small text-muted"},_hoisted_8$5={class:"text-center"},_hoisted_9$5=["onClick"],_hoisted_10$5={class:"col-md-12 mt-3"},_hoisted_11$4=createStaticVNode('<div class="form-group"><input class="form-control-file" id="file" multiple="" name="file" required="" type="file"><sub class="text-muted"> Attach multiple files using Control+Click or Cmd+Click. </sub></div><div class="form-group"><input class="btn btn-success float-right" id="_submit" name="_submit" type="submit" value="Upload"></div>',2),_hoisted_13$3=[_hoisted_11$4];function _sfc_render$6(e,t,i,n,a,o){return openBlock(),createElementBlock("div",null,[createBaseVNode("table",_hoisted_1$6,[_hoisted_2$6,createBaseVNode("tbody",null,[(openBlock(!0),createElementBlock(Fragment,null,renderList(e.files,s=>(openBlock(),createElementBlock("tr",{key:s.id},[createBaseVNode("td",_hoisted_3$6,[createBaseVNode("a",{href:`${e.urlRoot}/files/${s.location}`},toDisplayString(s.location.split("/").pop()),9,_hoisted_4$6),createBaseVNode("div",_hoisted_5$6,[_hoisted_6$6,createBaseVNode("span",_hoisted_7$5,toDisplayString(s.sha1sum||"null"),1)])]),createBaseVNode("td",_hoisted_8$5,[createBaseVNode("i",{role:"button",class:"btn-fa fas fa-times delete-file",onClick:l=>o.deleteFile(s.id)},null,8,_hoisted_9$5)])]))),128))])]),createBaseVNode("div",_hoisted_10$5,[createBaseVNode("form",{method:"POST",ref:"FileUploadForm",onSubmit:t[0]||(t[0]=withModifiers((...s)=>o.addFiles&&o.addFiles(...s),["prevent"]))},_hoisted_13$3,544)])])}const ChallengeFilesList=_export_sfc(_sfc_main$6,[["render",_sfc_render$6]]),_sfc_main$5={name:"HintCreationForm",props:{challenge_id:Number,hints:Array},data:function(){return{cost:0,selectedHints:[]}},methods:{clearForm:function(){this.$refs.title.value="",this.$refs.content&&this.$refs.content.mde&&(this.$refs.content.mde.value(""),this.$refs.content.mde.codemirror.refresh()),this.$refs.content.value="",this.cost=0,this.selectedHints=[]},getCost:function(){return this.cost||0},getContent:function(){return this.$refs.content.value},getTitle:function(){return this.$refs.title.value},submitHint:function(){let e={challenge_id:this.$props.challenge_id,content:this.getContent(),cost:this.getCost(),title:this.getTitle(),requirements:{prerequisites:this.selectedHints}};CTFd.fetch("/api/v1/hints",{method:"POST",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(e)}).then(t=>t.json()).then(t=>{t.success&&(this.$emit("refreshHints",this.$options.name),this.clearForm())})}}},_hoisted_1$5={class:"modal fade",tabindex:"-1"},_hoisted_2$5={class:"modal-dialog"},_hoisted_3$5={class:"modal-content"},_hoisted_4$5=createStaticVNode('<div class="modal-header text-center"><div class="container"><div class="row"><div class="col-md-12"><h3>Hint</h3></div></div></div><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button></div>',1),_hoisted_5$5={class:"modal-body"},_hoisted_6$5={class:"container"},_hoisted_7$4={class:"row"},_hoisted_8$4={class:"col-md-12"},_hoisted_9$4={class:"form-group"},_hoisted_10$4=createBaseVNode("label",null,[createTextVNode(" Title"),createBaseVNode("br"),createBaseVNode("small",null,"Content displayed before hint unlocking")],-1),_hoisted_11$3={type:"text",class:"form-control",name:"title",ref:"title"},_hoisted_12$3={class:"form-group"},_hoisted_13$2=createBaseVNode("label",null,[createTextVNode(" Hint"),createBaseVNode("br"),createBaseVNode("small",null,"Markdown & HTML are supported")],-1),_hoisted_14$2={type:"text",class:"form-control markdown",name:"content",rows:"7",ref:"content"},_hoisted_15$2={class:"form-group"},_hoisted_16$2=createBaseVNode("label",null,[createTextVNode(" Cost"),createBaseVNode("br"),createBaseVNode("small",null,"How many points it costs to see your hint.")],-1),_hoisted_17$2={class:"form-group"},_hoisted_18$2=createBaseVNode("label",null,[createTextVNode(" Requirements"),createBaseVNode("br"),createBaseVNode("small",null,"Hints that must be unlocked before unlocking this hint")],-1),_hoisted_19$2={class:"form-check-label cursor-pointer"},_hoisted_20$2=["value"],_hoisted_21$2=createBaseVNode("input",{type:"hidden",id:"hint-id-for-hint",name:"id"},null,-1),_hoisted_22$1=createStaticVNode('<div class="modal-footer"><div class="container"><div class="row"><div class="col-md-12"><button class="btn btn-primary float-right">Submit</button></div></div></div></div>',1);function _sfc_render$5(e,t,i,n,a,o){return openBlock(),createElementBlock("div",_hoisted_1$5,[createBaseVNode("div",_hoisted_2$5,[createBaseVNode("div",_hoisted_3$5,[_hoisted_4$5,createBaseVNode("form",{method:"POST",onSubmit:t[2]||(t[2]=withModifiers((...s)=>o.submitHint&&o.submitHint(...s),["prevent"]))},[createBaseVNode("div",_hoisted_5$5,[createBaseVNode("div",_hoisted_6$5,[createBaseVNode("div",_hoisted_7$4,[createBaseVNode("div",_hoisted_8$4,[createBaseVNode("div",_hoisted_9$4,[_hoisted_10$4,createBaseVNode("input",_hoisted_11$3,null,512)]),createBaseVNode("div",_hoisted_12$3,[_hoisted_13$2,createBaseVNode("textarea",_hoisted_14$2,null,512)]),createBaseVNode("div",_hoisted_15$2,[_hoisted_16$2,withDirectives(createBaseVNode("input",{type:"number",class:"form-control",name:"cost","onUpdate:modelValue":t[0]||(t[0]=s=>e.cost=s)},null,512),[[vModelText,e.cost,void 0,{lazy:!0}]])]),createBaseVNode("div",_hoisted_17$2,[_hoisted_18$2,(openBlock(!0),createElementBlock(Fragment,null,renderList(i.hints,s=>(openBlock(),createElementBlock("div",{class:"form-check",key:s.id},[createBaseVNode("label",_hoisted_19$2,[withDirectives(createBaseVNode("input",{class:"form-check-input",type:"checkbox",value:s.id,"onUpdate:modelValue":t[1]||(t[1]=l=>e.selectedHints=l)},null,8,_hoisted_20$2),[[vModelCheckbox,e.selectedHints]]),createTextVNode(" "+toDisplayString(s.cost)+" - "+toDisplayString(s.id),1)])]))),128))]),_hoisted_21$2])])])]),_hoisted_22$1],32)])])])}const HintCreationForm=_export_sfc(_sfc_main$5,[["render",_sfc_render$5]]),_sfc_main$4={name:"HintEditForm",props:{challenge_id:Number,hint_id:Number,hints:Array},data:function(){return{cost:0,title:null,content:null,selectedHints:[]}},computed:{otherHints:function(){return this.hints.filter(e=>e.id!==this.$props.hint_id)}},watch:{hint_id:{immediate:!0,handler(e,t){e!==null&&this.loadHint()}}},methods:{loadHint:function(){CTFd$1.fetch(`/api/v1/hints/${this.$props.hint_id}?preview=true`,{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(e=>e.json()).then(e=>{var t;if(e.success){let i=e.data;this.cost=i.cost,this.title=i.title,this.content=i.content,this.selectedHints=((t=i.requirements)==null?void 0:t.prerequisites)||[];let n=this.$refs.content;bindMarkdownEditor(n),setTimeout(()=>{n.mde.codemirror.getDoc().setValue(n.value),this._forceRefresh()},200)}})},_forceRefresh:function(){this.$refs.content.mde.codemirror.refresh()},getCost:function(){return this.cost||0},getContent:function(){return this._forceRefresh(),this.$refs.content.mde.codemirror.getDoc().getValue()},getTitle:function(){return this.$refs.title.value},updateHint:function(){let e={challenge_id:this.$props.challenge_id,content:this.getContent(),cost:this.getCost(),title:this.getTitle(),requirements:{prerequisites:this.selectedHints}};CTFd$1.fetch(`/api/v1/hints/${this.$props.hint_id}`,{method:"PATCH",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(e)}).then(t=>t.json()).then(t=>{t.success&&this.$emit("refreshHints",this.$options.name)})}},mounted(){this.hint_id&&this.loadHint()},created(){this.hint_id&&this.loadHint()}},_hoisted_1$4={class:"modal fade",tabindex:"-1"},_hoisted_2$4={class:"modal-dialog"},_hoisted_3$4={class:"modal-content"},_hoisted_4$4=createStaticVNode('<div class="modal-header text-center"><div class="container"><div class="row"><div class="col-md-12"><h3>Hint</h3></div></div></div><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button></div>',1),_hoisted_5$4={class:"modal-body"},_hoisted_6$4={class:"container"},_hoisted_7$3={class:"row"},_hoisted_8$3={class:"col-md-12"},_hoisted_9$3={class:"form-group"},_hoisted_10$3=createBaseVNode("label",null,[createTextVNode(" Title"),createBaseVNode("br"),createBaseVNode("small",null,"Content displayed before hint unlocking")],-1),_hoisted_11$2=["value"],_hoisted_12$2={class:"form-group"},_hoisted_13$1=createBaseVNode("label",null,[createTextVNode(" Hint"),createBaseVNode("br"),createBaseVNode("small",null,"Markdown & HTML are supported")],-1),_hoisted_14$1=["value"],_hoisted_15$1={class:"form-group"},_hoisted_16$1=createBaseVNode("label",null,[createTextVNode(" Cost"),createBaseVNode("br"),createBaseVNode("small",null,"How many points it costs to see your hint.")],-1),_hoisted_17$1={class:"form-group"},_hoisted_18$1=createBaseVNode("label",null,[createTextVNode(" Requirements"),createBaseVNode("br"),createBaseVNode("small",null,"Hints that must be unlocked before unlocking this hint")],-1),_hoisted_19$1={class:"form-check-label cursor-pointer"},_hoisted_20$1=["value"],_hoisted_21$1=createStaticVNode('<div class="modal-footer"><div class="container"><div class="row"><div class="col-md-12"><button class="btn btn-primary float-right">Submit</button></div></div></div></div>',1);function _sfc_render$4(e,t,i,n,a,o){return openBlock(),createElementBlock("div",_hoisted_1$4,[createBaseVNode("div",_hoisted_2$4,[createBaseVNode("div",_hoisted_3$4,[_hoisted_4$4,createBaseVNode("form",{method:"POST",onSubmit:t[2]||(t[2]=withModifiers((...s)=>o.updateHint&&o.updateHint(...s),["prevent"]))},[createBaseVNode("div",_hoisted_5$4,[createBaseVNode("div",_hoisted_6$4,[createBaseVNode("div",_hoisted_7$3,[createBaseVNode("div",_hoisted_8$3,[createBaseVNode("div",_hoisted_9$3,[_hoisted_10$3,createBaseVNode("input",{type:"text",class:"form-control",name:"title",value:this.title,ref:"title"},null,8,_hoisted_11$2)]),createBaseVNode("div",_hoisted_12$2,[_hoisted_13$1,createBaseVNode("textarea",{type:"text",class:"form-control",name:"content",rows:"7",value:this.content,ref:"content"},null,8,_hoisted_14$1)]),createBaseVNode("div",_hoisted_15$1,[_hoisted_16$1,withDirectives(createBaseVNode("input",{type:"number",class:"form-control",name:"cost","onUpdate:modelValue":t[0]||(t[0]=s=>e.cost=s)},null,512),[[vModelText,e.cost,void 0,{lazy:!0}]])]),createBaseVNode("div",_hoisted_17$1,[_hoisted_18$1,(openBlock(!0),createElementBlock(Fragment,null,renderList(o.otherHints,s=>(openBlock(),createElementBlock("div",{class:"form-check",key:s.id},[createBaseVNode("label",_hoisted_19$1,[withDirectives(createBaseVNode("input",{class:"form-check-input",type:"checkbox",value:s.id,"onUpdate:modelValue":t[1]||(t[1]=l=>e.selectedHints=l)},null,8,_hoisted_20$1),[[vModelCheckbox,e.selectedHints]]),createTextVNode(" "+toDisplayString(s.content)+" - "+toDisplayString(s.cost),1)])]))),128))])])])])]),_hoisted_21$1],32)])])])}const HintEditForm=_export_sfc(_sfc_main$4,[["render",_sfc_render$4]]),_sfc_main$3={components:{HintCreationForm,HintEditForm},props:{challenge_id:Number},data:function(){return{hints:[],editing_hint_id:null}},methods:{loadHints:async function(){let t=await(await CTFd$1.fetch(`/api/v1/challenges/${this.$props.challenge_id}/hints`,{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}})).json();return this.hints=t.data,t.success},addHint:function(){let e=this.$refs.HintCreationForm.$el;$(e).modal()},editHint:function(e){this.editing_hint_id=e;let t=this.$refs.HintEditForm.$el;$(t).modal()},refreshHints:function(e){this.loadHints().then(t=>{if(t){let i;switch(e){case"HintCreationForm":i=this.$refs.HintCreationForm.$el,console.log(i),$(i).modal("hide");break;case"HintEditForm":i=this.$refs.HintEditForm.$el,$(i).modal("hide");break}}else alert("An error occurred while updating this hint. Please try again.")})},deleteHint:function(e){ezQuery({title:"Delete Hint",body:"Are you sure you want to delete this hint?",success:()=>{CTFd$1.fetch(`/api/v1/hints/${e}`,{method:"DELETE"}).then(t=>t.json()).then(t=>{t.success&&this.loadHints()})}})}},created(){this.loadHints()}},_hoisted_1$3={class:"table table-striped"},_hoisted_2$3=createBaseVNode("thead",null,[createBaseVNode("tr",null,[createBaseVNode("td",{class:"text-center"},[createBaseVNode("b",null,"ID")]),createBaseVNode("td",{class:"text-center"},[createBaseVNode("b",null,"Title")]),createBaseVNode("td",{class:"text-center"},[createBaseVNode("b",null,"Hint")]),createBaseVNode("td",{class:"text-center"},[createBaseVNode("b",null,"Cost")]),createBaseVNode("td",{class:"text-center"},[createBaseVNode("b",null,"Settings")])])],-1),_hoisted_3$3={class:"text-center"},_hoisted_4$3={class:"text-center"},_hoisted_5$3={class:"text-break"},_hoisted_6$3={class:"text-center"},_hoisted_7$2={class:"text-center"},_hoisted_8$2=["onClick"],_hoisted_9$2=["onClick"],_hoisted_10$2={class:"col-md-12"};function _sfc_render$3(e,t,i,n,a,o){const s=resolveComponent("HintCreationForm"),l=resolveComponent("HintEditForm");return openBlock(),createElementBlock("div",null,[createBaseVNode("div",null,[createVNode(s,{ref:"HintCreationForm",challenge_id:i.challenge_id,hints:e.hints,onRefreshHints:o.refreshHints},null,8,["challenge_id","hints","onRefreshHints"])]),createBaseVNode("div",null,[createVNode(l,{ref:"HintEditForm",challenge_id:i.challenge_id,hint_id:e.editing_hint_id,hints:e.hints,onRefreshHints:o.refreshHints},null,8,["challenge_id","hint_id","hints","onRefreshHints"])]),createBaseVNode("table",_hoisted_1$3,[_hoisted_2$3,createBaseVNode("tbody",null,[(openBlock(!0),createElementBlock(Fragment,null,renderList(e.hints,d=>(openBlock(),createElementBlock("tr",{key:d.id},[createBaseVNode("td",_hoisted_3$3,toDisplayString(d.type),1),createBaseVNode("td",_hoisted_4$3,toDisplayString(d.title),1),createBaseVNode("td",_hoisted_5$3,[createBaseVNode("pre",null,toDisplayString(d.content),1)]),createBaseVNode("td",_hoisted_6$3,toDisplayString(d.cost),1),createBaseVNode("td",_hoisted_7$2,[createBaseVNode("i",{role:"button",class:"btn-fa fas fa-edit",onClick:r=>o.editHint(d.id)},null,8,_hoisted_8$2),createBaseVNode("i",{role:"button",class:"btn-fa fas fa-times",onClick:r=>o.deleteHint(d.id)},null,8,_hoisted_9$2)])]))),128))])]),createBaseVNode("div",_hoisted_10$2,[createBaseVNode("button",{class:"btn btn-success float-right",onClick:t[0]||(t[0]=(...d)=>o.addHint&&o.addHint(...d))}," Create Hint ")])])}const HintsList=_export_sfc(_sfc_main$3,[["render",_sfc_render$3]]),_sfc_main$2={props:{challenge_id:Number},data:function(){return{challenge:null,challenges:[],selected_id:null}},computed:{updateAvailable:function(){return this.challenge?this.selected_id!=this.challenge.next_id:!1},otherChallenges:function(){return this.challenges.filter(e=>e.id!==this.$props.challenge_id)}},methods:{loadData:function(){CTFd$1.fetch(`/api/v1/challenges/${this.$props.challenge_id}`,{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(e=>e.json()).then(e=>{e.success&&(this.challenge=e.data,this.selected_id=e.data.next_id)})},loadChallenges:function(){CTFd$1.fetch("/api/v1/challenges?view=admin",{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(e=>e.json()).then(e=>{e.success&&(this.challenges=e.data)})},updateNext:function(){CTFd$1.fetch(`/api/v1/challenges/${this.$props.challenge_id}`,{method:"PATCH",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify({next_id:this.selected_id!="null"?this.selected_id:null})}).then(e=>e.json()).then(e=>{e.success&&(this.loadData(),this.loadChallenges())})}},created(){this.loadData(),this.loadChallenges()}},_hoisted_1$2={class:"form-group"},_hoisted_2$2=createBaseVNode("label",null,[createTextVNode(" Next Challenge "),createBaseVNode("br"),createBaseVNode("small",{class:"text-muted"},"Challenge to recommend after solving this challenge")],-1),_hoisted_3$2=createBaseVNode("option",{value:"null"},"--",-1),_hoisted_4$2=["value"],_hoisted_5$2={class:"form-group"},_hoisted_6$2=["disabled"];function _sfc_render$2(e,t,i,n,a,o){return openBlock(),createElementBlock("div",null,[createBaseVNode("form",{onSubmit:t[1]||(t[1]=withModifiers((...s)=>o.updateNext&&o.updateNext(...s),["prevent"]))},[createBaseVNode("div",_hoisted_1$2,[_hoisted_2$2,withDirectives(createBaseVNode("select",{class:"form-control custom-select","onUpdate:modelValue":t[0]||(t[0]=s=>e.selected_id=s)},[_hoisted_3$2,(openBlock(!0),createElementBlock(Fragment,null,renderList(o.otherChallenges,s=>(openBlock(),createElementBlock("option",{value:s.id,key:s.id},toDisplayString(s.name),9,_hoisted_4$2))),128))],512),[[vModelSelect,e.selected_id]])]),createBaseVNode("div",_hoisted_5$2,[createBaseVNode("button",{class:"btn btn-success float-right",disabled:!o.updateAvailable}," Save ",8,_hoisted_6$2)])],32)])}const NextChallenge=_export_sfc(_sfc_main$2,[["render",_sfc_render$2]]),_sfc_main$1={name:"SolutionEditor",props:{challenge_id:Number},data:function(){return{solution_id:null,content:"",state:"hidden",loading:!1}},watch:{solution_id:{handler(e,t){t==null&&this.loadSolution()}}},methods:{loadSolution:function(){CTFd$1.fetch(`/api/v1/solutions/${this.solution_id}`,{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(e=>e.json()).then(e=>{if(e.success){let t=e.data;this.content=t.content||"",this.state=t.state||"hidden";let i=this.$refs.content;bindMarkdownEditor(i),setTimeout(()=>{i.mde&&(i.mde.codemirror.getDoc().setValue(this.content),this._forceRefresh())},200)}}).catch(e=>{console.error("Error loading solution:",e)})},resetForm:function(){this.content="",this.state="hidden",setTimeout(()=>{let e=this.$refs.content;e&&(bindMarkdownEditor(e),setTimeout(()=>{e.mde&&(e.mde.codemirror.getDoc().setValue(""),this._forceRefresh())},200))},100)},_forceRefresh:function(){let e=this.$refs.content;e&&e.mde&&e.mde.codemirror.refresh()},getContent:function(){this._forceRefresh();let e=this.$refs.content;return e&&e.mde?e.mde.codemirror.getDoc().getValue():e.value},submitSolution:function(){this.loading=!0;let e={content:this.getContent(),state:this.state},t,i;this.solution_id?(t=`/api/v1/solutions/${this.solution_id}`,i="PATCH"):(e.challenge_id=this.challenge_id,t="/api/v1/solutions",i="POST"),CTFd$1.fetch(t,{method:i,credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(e)}).then(n=>n.json()).then(n=>{n.success?(this.solution_id||(this.solution_id=n.data.id),this.loading=!1):(this.loading=!1,console.error("Error submitting solution:",n.errors))}).catch(n=>{this.loading=!1,console.error("Network error:",n)})}},created(){this.solution_id=window.CHALLENGE_SOLUTION_ID,$("a[href='#solution']").on("shown.bs.tab",e=>{this._forceRefresh()}),this.solution_id?this.loadSolution():this.resetForm()}},_hoisted_1$1={class:"form-group"},_hoisted_2$1=createBaseVNode("label",null,[createTextVNode(" Content"),createBaseVNode("br"),createBaseVNode("small",null,"Markdown & HTML are supported")],-1),_hoisted_3$1=["value","media-id"],_hoisted_4$1={class:"form-group"},_hoisted_5$1=createBaseVNode("label",null,[createTextVNode(" State"),createBaseVNode("br"),createBaseVNode("small",null,"Controls who can view this solution")],-1),_hoisted_6$1=createBaseVNode("option",{value:"hidden"},"Hidden",-1),_hoisted_7$1=createBaseVNode("option",{value:"visible"},"Visible",-1),_hoisted_8$1=[_hoisted_6$1,_hoisted_7$1],_hoisted_9$1={class:"btn btn-primary float-right",type:"submit"},_hoisted_10$1={key:0,class:"spinner-border spinner-border-sm ml-2",role:"status"},_hoisted_11$1=createBaseVNode("span",{class:"sr-only"},"Loading...",-1),_hoisted_12$1=[_hoisted_11$1];function _sfc_render$1(e,t,i,n,a,o){return openBlock(),createElementBlock("div",null,[createBaseVNode("form",{method:"POST",onSubmit:t[1]||(t[1]=withModifiers((...s)=>o.submitSolution&&o.submitSolution(...s),["prevent"]))},[createBaseVNode("div",_hoisted_1$1,[_hoisted_2$1,createBaseVNode("textarea",{type:"text",class:"form-control",name:"content",rows:"10",value:this.content,"media-type":"solution","media-id-title":"solution_id","media-id":this.solution_id,ref:"content"},null,8,_hoisted_3$1)]),createBaseVNode("div",_hoisted_4$1,[_hoisted_5$1,withDirectives(createBaseVNode("select",{class:"form-control custom-select",name:"state","onUpdate:modelValue":t[0]||(t[0]=s=>e.state=s)},_hoisted_8$1,512),[[vModelSelect,e.state]])]),createBaseVNode("button",_hoisted_9$1,toDisplayString(e.solution_id?"Update":"Create")+" Solution ",1),e.loading?(openBlock(),createElementBlock("div",_hoisted_10$1,_hoisted_12$1)):createCommentVNode("",!0)],32)])}const SolutionEditor=_export_sfc(_sfc_main$1,[["render",_sfc_render$1]]),_sfc_main={name:"RatingsViewer",props:{challengeId:{type:[String,Number],required:!0}},data(){return{loading:!1,error:null,ratings:[],meta:{summary:{average:null,count:0},pagination:{page:1,pages:1,prev:null,next:null,per_page:50,total:0}},urlRoot:CTFd$1.config.urlRoot}},created(){document.getElementById("ratings-viewer-load").addEventListener("click",this.onModalShow)},methods:{onModalShow(){this.loadRatings()},async loadRatings(e=1){this.loading=!0,this.error=null;try{const i=await(await CTFd$1.fetch(`/api/v1/challenges/${this.challengeId}/ratings?page=${e}`,{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}})).json();i.success?(this.ratings=i.data,this.meta=i.meta):(this.error="Failed to load ratings",console.error("API Error:",i))}catch(t){console.error("Error loading ratings:",t),this.error="Error loading ratings"}finally{this.loading=!1}},loadPage(e){this.loadRatings(e)},toLocalTime(e){return dayjs(e).format("MMMM Do, h:mm:ss A")}}},_hoisted_1={class:"row mb-3"},_hoisted_2={class:"col-md-12"},_hoisted_3={key:0,class:"text-center"},_hoisted_4=createBaseVNode("i",{class:"fas fa-circle-notch fa-spin spinner"},null,-1),_hoisted_5={key:1,class:"alert alert-danger"},_hoisted_6={key:2,class:"text-center text-muted py-4"},_hoisted_7=createBaseVNode("i",{class:"fa fa-star fa-2x mb-3"},null,-1),_hoisted_8=createBaseVNode("p",null,"No ratings yet",-1),_hoisted_9=[_hoisted_7,_hoisted_8],_hoisted_10={key:3},_hoisted_11={key:0,class:"mb-3"},_hoisted_12={class:"row"},_hoisted_13={class:"col-md-4 text-center"},_hoisted_14={class:"text-success"},_hoisted_15=createBaseVNode("i",{class:"fa-solid fa-thumbs-up"},null,-1),_hoisted_16={class:"col-md-4 text-center"},_hoisted_17={class:"text-danger"},_hoisted_18=createBaseVNode("i",{class:"fa-solid fa-thumbs-down"},null,-1),_hoisted_19={class:"col-md-4 text-center"},_hoisted_20={class:"ratings-list"},_hoisted_21={class:"row border rounded p-3"},_hoisted_22={class:"col-md-8 p-0"},_hoisted_23={class:"mb-1"},_hoisted_24=["href"],_hoisted_25={class:"ml-2"},_hoisted_26={key:0,class:"text-success"},_hoisted_27=createBaseVNode("i",{class:"fa-solid fa-thumbs-up"},null,-1),_hoisted_28=[_hoisted_27],_hoisted_29={key:1,class:"text-danger"},_hoisted_30=createBaseVNode("i",{class:"fa-solid fa-thumbs-down"},null,-1),_hoisted_31=[_hoisted_30],_hoisted_32={key:0},_hoisted_33={class:"col-md-4 text-right p-0"},_hoisted_34={class:"text-muted"},_hoisted_35={key:1,class:"d-flex justify-content-between align-items-center mt-3"},_hoisted_36=["disabled"],_hoisted_37=createBaseVNode("i",{class:"fa fa-arrow-left"},null,-1),_hoisted_38={key:1},_hoisted_39={class:"text-muted"},_hoisted_40=["disabled"],_hoisted_41=createBaseVNode("i",{class:"fa fa-arrow-right"},null,-1),_hoisted_42={key:3};function _sfc_render(e,t,i,n,a,o){return openBlock(),createElementBlock("div",null,[createBaseVNode("div",_hoisted_1,[createBaseVNode("div",_hoisted_2,[a.loading?(openBlock(),createElementBlock("div",_hoisted_3,[_hoisted_4,createTextVNode(" Loading ratings... ")])):a.error?(openBlock(),createElementBlock("div",_hoisted_5,toDisplayString(a.error),1)):!a.ratings.length&&!a.loading?(openBlock(),createElementBlock("div",_hoisted_6,_hoisted_9)):(openBlock(),createElementBlock("div",_hoisted_10,[a.meta.summary.count>0?(openBlock(),createElementBlock("div",_hoisted_11,[createBaseVNode("div",_hoisted_12,[createBaseVNode("div",_hoisted_13,[createBaseVNode("h4",null,[createBaseVNode("strong",_hoisted_14,[_hoisted_15,createTextVNode(" "+toDisplayString(a.meta.summary.up),1)])])]),createBaseVNode("div",_hoisted_16,[createBaseVNode("h4",null,[createBaseVNode("strong",_hoisted_17,[_hoisted_18,createTextVNode(" "+toDisplayString(a.meta.summary.down),1)])])]),createBaseVNode("div",_hoisted_19,[createBaseVNode("h4",null,[createBaseVNode("strong",null," Total: "+toDisplayString(a.meta.summary.count),1)])])])])):createCommentVNode("",!0),createBaseVNode("div",_hoisted_20,[(openBlock(!0),createElementBlock(Fragment,null,renderList(a.ratings,s=>(openBlock(),createElementBlock("div",{key:s.id,class:"mb-2"},[createBaseVNode("div",_hoisted_21,[createBaseVNode("div",_hoisted_22,[createBaseVNode("h6",_hoisted_23,[createBaseVNode("a",{href:`${a.urlRoot}/admin/users/${s.user.id}`},toDisplayString(s.user.name),9,_hoisted_24),createBaseVNode("span",_hoisted_25,[s.value===1?(openBlock(),createElementBlock("span",_hoisted_26,_hoisted_28)):s.value===-1?(openBlock(),createElementBlock("span",_hoisted_29,_hoisted_31)):createCommentVNode("",!0)])]),s.review?(openBlock(),createElementBlock("p",_hoisted_32,toDisplayString(s.review),1)):createCommentVNode("",!0)]),createBaseVNode("div",_hoisted_33,[createBaseVNode("small",_hoisted_34,toDisplayString(o.toLocalTime(s.date)),1)])])]))),128))]),a.meta.pagination.pages>1?(openBlock(),createElementBlock("div",_hoisted_35,[a.meta.pagination.prev?(openBlock(),createElementBlock("button",{key:0,onClick:t[0]||(t[0]=s=>o.loadPage(a.meta.pagination.prev)),class:"btn btn-secondary",disabled:a.loading},[_hoisted_37,createTextVNode(" Previous ")],8,_hoisted_36)):(openBlock(),createElementBlock("div",_hoisted_38)),createBaseVNode("span",_hoisted_39," Page "+toDisplayString(a.meta.pagination.page)+" of "+toDisplayString(a.meta.pagination.pages),1),a.meta.pagination.next?(openBlock(),createElementBlock("button",{key:2,onClick:t[1]||(t[1]=s=>o.loadPage(a.meta.pagination.next)),class:"btn btn-secondary",disabled:a.loading},[createTextVNode(" Next "),_hoisted_41],8,_hoisted_40)):(openBlock(),createElementBlock("div",_hoisted_42))])):createCommentVNode("",!0)]))])])])}const RatingsViewer=_export_sfc(_sfc_main,[["render",_sfc_render]]);function loadChalTemplate(e){CTFd$1._internal.challenge={},$$1.getScript(CTFd$1.config.urlRoot+e.scripts.view,function(){let t=e.create;$$1("#create-chal-entry-div").html(t),bindMarkdownEditors(),$$1.getScript(CTFd$1.config.urlRoot+e.scripts.create,function(){$$1("#create-chal-entry-div form").submit(function(i){i.preventDefault();const n=$$1("#create-chal-entry-div form").serializeJSON();CTFd$1.fetch("/api/v1/challenges",{method:"POST",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(n)}).then(function(a){return a.json()}).then(function(a){if(a.success)$$1("#challenge-create-options #challenge_id").val(a.data.id),$$1("#challenge-create-options").modal();else{let o="";for(const s in a.errors)o+=a.errors[s].join(`
`),o+=`
`;ezAlert({title:"Error",body:o,button:"OK"})}})})})})}function handleChallengeOptions(e){e.preventDefault();var t=$$1(e.target).serializeJSON(!0);let i={challenge_id:t.challenge_id,content:t.flag||"",type:t.flag_type,data:t.flag_data?t.flag_data:""},n=function(){CTFd$1.fetch("/api/v1/challenges/"+t.challenge_id,{method:"PATCH",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify({state:t.state})}).then(function(a){return a.json()}).then(function(a){a.success&&setTimeout(function(){window.location=CTFd$1.config.urlRoot+"/admin/challenges/"+t.challenge_id},700)})};Promise.all([new Promise(function(a,o){if(i.content.length==0){a();return}CTFd$1.fetch("/api/v1/flags",{method:"POST",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(i)}).then(function(s){a(s.json())})}),new Promise(function(a,o){let s=e.target,l={challenge:t.challenge_id,type:"challenge"};$$1(s.elements.file).val()&&helpers.files.upload(s,l),a()})]).then(a=>{n()})}$$1(()=>{if($$1(".preview-challenge").click(function(e){let t=`${CTFd$1.config.urlRoot}/admin/challenges/preview/${window.CHALLENGE_ID}`;$$1("#challenge-window").html(`<iframe src="${t}" height="100%" width="100%" frameBorder=0></iframe>`),$$1("#challenge-modal").modal()}),$$1(".comments-challenge").click(function(e){$$1("#challenge-comments-window").modal()}),$$1(".ratings-challenge").click(function(e){$$1("#challenge-ratings-window").modal()}),$$1(".delete-challenge").click(function(e){ezQuery({title:"Delete Challenge",body:`Are you sure you want to delete <strong>${htmlEntities(window.CHALLENGE_NAME)}</strong>`,success:function(){CTFd$1.fetch("/api/v1/challenges/"+window.CHALLENGE_ID,{method:"DELETE"}).then(function(t){return t.json()}).then(function(t){t.success&&(window.location=CTFd$1.config.urlRoot+"/admin/challenges")})}})}),$$1("#challenge-update-container > form").submit(function(e){e.preventDefault();var t=$$1(e.target).serializeJSON(!0);CTFd$1.fetch("/api/v1/challenges/"+window.CHALLENGE_ID+"/flags",{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(function(i){return i.json()}).then(function(i){let n=function(){CTFd$1.fetch("/api/v1/challenges/"+window.CHALLENGE_ID,{method:"PATCH",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(t)}).then(function(a){return a.json()}).then(function(a){if(a.success){switch($$1(".challenge-state").text(a.data.state),a.data.state){case"visible":$$1(".challenge-state").removeClass("badge-danger").addClass("badge-success");break;case"hidden":$$1(".challenge-state").removeClass("badge-success").addClass("badge-danger");break}ezToast({title:"Success",body:"Your challenge has been updated!"})}else{let o="";for(const s in a.errors)o+=a.errors[s].join(`
`),o+=`
`;ezAlert({title:"Error",body:o,button:"OK"})}})};i.data.length===0&&t.state==="visible"?ezQuery({title:"Missing Flags",body:"This challenge does not have any flags meaning it may be unsolveable. Are you sure you'd like to update this challenge?",success:n}):n()})}),$$1("#challenge-create-options form").submit(handleChallengeOptions),$$1(".chal-function").change(function(){const e=$$1(this).val(),t=$$1(".chal-initial").closest(".form-group"),i=$$1(".chal-decay").closest(".form-group"),n=$$1(".chal-minimum").closest(".form-group"),a=$$1(".chal-initial"),o=$$1(".chal-decay"),s=$$1(".chal-minimum"),l=$$1(".chal-value");e==="static"?(a.val()&&a.data("saved-value",a.val()),o.val()&&o.data("saved-value",o.val()),s.val()&&s.data("saved-value",s.val()),a.val(""),o.val(""),s.val(""),t.hide(),i.hide(),n.hide(),a.removeAttr("name").data("original-name","initial"),o.removeAttr("name").data("original-name","decay"),s.removeAttr("name").data("original-name","minimum"),a.removeAttr("required"),o.removeAttr("required"),s.removeAttr("required"),l.prop("disabled",!1).prop("required",!0)):(e==="linear"||e==="logarithmic")&&(t.show(),i.show(),n.show(),a.attr("name",a.data("original-name")||"initial"),o.attr("name",o.data("original-name")||"decay"),s.attr("name",s.data("original-name")||"minimum"),a.data("saved-value")&&a.val(a.data("saved-value")),o.data("saved-value")&&o.val(o.data("saved-value")),s.data("saved-value")&&s.val(s.data("saved-value")),a.prop("required",!0),o.prop("required",!0),s.prop("required",!0),l.prop("disabled",!0).prop("required",!1))}).trigger("change"),document.querySelector("#challenge-flags")){const e=Vue$1.extend(FlagList);let t=document.createElement("div");document.querySelector("#challenge-flags").appendChild(t),new e({propsData:{challenge_id:window.CHALLENGE_ID}}).$mount(t)}if(document.querySelector("#challenge-topics")){const e=Vue$1.extend(TopicsList);let t=document.createElement("div");document.querySelector("#challenge-topics").appendChild(t),new e({propsData:{challenge_id:window.CHALLENGE_ID}}).$mount(t)}if(document.querySelector("#challenge-tags")){const e=Vue$1.extend(TagsList);let t=document.createElement("div");document.querySelector("#challenge-tags").appendChild(t),new e({propsData:{challenge_id:window.CHALLENGE_ID}}).$mount(t)}if(document.querySelector("#prerequisite-add-form")){const e=Vue$1.extend(Requirements);let t=document.createElement("div");document.querySelector("#prerequisite-add-form").appendChild(t),new e({propsData:{challenge_id:window.CHALLENGE_ID}}).$mount(t)}if(document.querySelector("#challenge-files")){const e=Vue$1.extend(ChallengeFilesList);let t=document.createElement("div");document.querySelector("#challenge-files").appendChild(t),new e({propsData:{challenge_id:window.CHALLENGE_ID}}).$mount(t)}if(document.querySelector("#challenge-hints")){const e=Vue$1.extend(HintsList);let t=document.createElement("div");document.querySelector("#challenge-hints").appendChild(t),new e({propsData:{challenge_id:window.CHALLENGE_ID}}).$mount(t)}if(document.querySelector("#next-add-form")){const e=Vue$1.extend(NextChallenge);let t=document.createElement("div");document.querySelector("#next-add-form").appendChild(t),new e({propsData:{challenge_id:window.CHALLENGE_ID}}).$mount(t)}if(document.querySelector("#challenge-solution")){const e=Vue$1.extend(SolutionEditor);let t=document.createElement("div");document.querySelector("#challenge-solution").appendChild(t),new e({propsData:{challenge_id:window.CHALLENGE_ID}}).$mount(t)}if(document.querySelector("#comment-box")){const e=Vue$1.extend(CommentBox);let t=document.createElement("div");document.querySelector("#comment-box").appendChild(t),new e({propsData:{type:"challenge",id:window.CHALLENGE_ID}}).$mount(t)}if(document.querySelector("#ratings-box")){const e=Vue$1.extend(RatingsViewer);let t=document.createElement("div");document.querySelector("#ratings-box").appendChild(t),new e({propsData:{challengeId:window.CHALLENGE_ID}}).$mount(t)}$$1.get(CTFd$1.config.urlRoot+"/api/v1/challenges/types",function(e){const t=e.data;loadChalTemplate(t.standard),$$1("#create-chals-select input[name=type]").change(function(){let i=t[this.value];loadChalTemplate(i)})})});

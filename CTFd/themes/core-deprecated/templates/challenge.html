<div class="modal-dialog" role="document">
	<div class="modal-content">
		<div class="modal-body">
			<button type="button" class="close" data-dismiss="modal" aria-label="Close">
				<span aria-hidden="true">&times;</span>
			</button>
			<ul class="nav nav-tabs">
				<li class="nav-item">
					<a class="nav-link active" href="#challenge">Challenge</a>
				</li>
				{% block solves %}
					<li class="nav-item">
						<a class="nav-link challenge-solves" href="#solves">
							{% if solves != None %}
								{{ solves }} {% if solves > 1 %}Solves{% else %}Solves{% endif %}
							{% endif %}
						</a>
					</li>
				{% endblock %}
			</ul>
			<div role="tabpanel">
				<div class="tab-content">
					<div role="tabpanel" class="tab-pane fade show active" id="challenge">
						<h2 class='challenge-name text-center pt-3'>
							{{ challenge.name }}
						</h2>
						<h3 class="challenge-value text-center">
							{{ challenge.value }}
						</h3>

						<div class="challenge-tags text-center">
							{% block tags %}
								{% for tag in tags %}
									<span class='badge badge-info challenge-tag'>{{ tag }}</span>
								{% endfor %}
							{% endblock %}
						</div>

						<span class="challenge-desc">{% block description %}{{ challenge.html }}{% endblock %}</span>

						<span class="challenge-connection-info">
							{% block connection_info %}
								{% set conn = challenge.connection_info %}
								{% if not conn %}
								{% elif conn.startswith("http") %}
									{{ conn | urlize(target="_blank") }}
								{% else %}
									<code>{{ conn }}</code>
								{% endif %}
							{% endblock %}
						</span>

						<div class="challenge-hints hint-row row">
							{% for hint in hints %}
								<div class='col-md-12 hint-button-wrapper text-center mb-3'>
									<a class="btn btn-info btn-hint btn-block load-hint" href="javascript:;" data-hint-id="{{ hint.id }}">
										{% if hint.content %}
											<small>
												View Hint
											</small>
										{% else %}
											{% if hint.cost %}
												<small>
													Unlock Hint for {{ hint.cost }} points
												</small>
											{% else %}
												<small>
													View Hint
												</small>
											{% endif %}
										{% endif %}
									</a>
								</div>
							{% endfor %}
						</div>

						<div class="row challenge-files text-center pb-3">
							{% for file in files %}
								<div class='col-md-4 col-sm-4 col-xs-12 file-button-wrapper d-block'>
									<a class='btn btn-info btn-file mb-1 d-inline-block px-2 w-100 text-truncate'
									   href='{{ file }}'>
										<i class="fas fa-download"></i>
										<small>
											{% set segments = file.split('/') %}
											{% set file = segments | last %}
											{% set token = file.split('?') | last %}
											{% if token %}
												{{ file | replace("?" + token, "") }}
											{% else %}
												{{ file }}
											{% endif %}
										</small>
									</a>
								</div>
							{% endfor %}
						</div>

						{% if max_attempts > 0 %}
						<div class="row text-center">
							<div class="col-md-12">
								<p>
									{{ attempts }}/{{ max_attempts }} attempt{{ max_attempts|pluralize }}
								</p>
							</div>
						</div>
						{% endif %}

						<div class="row submit-row">
							<div class="col-md-9 form-group">
								{% block input %}
									<input id="challenge-id" class="challenge-id" type="hidden" value="{{ challenge.id }}">
									<input id="challenge-input" class="challenge-input" type="text" name="answer" placeholder="Flag"/>
								{% endblock %}
							</div>
							<div class="col-md-3 form-group key-submit">
								{% block submit %}
								<button id="challenge-submit" class="challenge-submit" type="submit">
									Submit
								</button>
								{% endblock %}
							</div>
						</div>

						<div class="row notification-row">
							<div class="col-md-12">
								<div id="result-notification" class="alert alert-dismissable text-center w-100"
									 role="alert" style="display: none;">
									<strong id="result-message"></strong>
								</div>
							</div>
						</div>
					</div>

					<div role="tabpanel" class="tab-pane fade" id="solves">
						<div class="row">
							<div class="col-md-12">
								<table class="table table-striped text-center">
									<thead>
									<tr>
										<td><b>Name</b>
										</td>
										<td><b>Date</b>
										</td>
									</tr>
									</thead>
									<tbody id="challenge-solves-names">
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

{% extends "base.html" %}

{% block stylesheets %}
{% endblock %}

{% block content %}
	<div class="jumbotron">
		<div class="container">
			<h1 id="team-id" team-id="{{ team.id }}">{{ team.name }}</h1>
			{% if team.oauth_id %}
				<a href="https://majorleaguecyber.org/t/{{ team.name }}">
					<h3><span class="badge badge-primary">Official</span></h3>
				</a>
			{% endif %}
			{% if team.affiliation %}
				<h3 class="d-inline-block">
					<span class="badge badge-primary">{{ team.affiliation }}</span>
				</h3>
			{% endif %}
			{% if team.country %}
				<h3 class="d-inline-block">
					<span class="badge badge-primary">
						<i class="flag-{{ team.country.lower() }}"></i>
						{{ lookup_country_code(team.country) }}
					</span>
				</h3>
			{% endif %}
			{% for field in team.fields %}
				<h3 class="d-block">
					{{ field.name }}: {{ field.value }}
				</h3>
			{% endfor %}
			<h2 id="team-place" class="text-center">
				{# This intentionally hides the team's place when scores are hidden because this can be their internal profile
			and we don't want to leak their place in the CTF. #}
				{# Public page hiding is done at the route level #}
				{% if scores_visible() %}
					{% if place %}
						{{ place }}
						<small>place</small>
					{% endif %}
				{% endif %}
			</h2>
			<h2 id="team-score" class="text-center">
				{% if score %}
					{{ score }}
					<small>points</small>
				{% endif %}
			</h2>

			<div class="pt-3">
				{% if team.website and (team.website.startswith('http://') or team.website.startswith('https://')) %}
					<a href="{{ team.website }}" target="_blank" style="color: inherit;" rel="noopener">
						<i class="fas fa-external-link-alt fa-2x px-2" data-toggle="tooltip" data-placement="top"
						   title="{{ team.website }}"></i>
					</a>
				{% endif %}
			</div>
		</div>
	</div>
	<div class="container">
		{% include "components/errors.html" %}

		<br>

		<div class="row">
			<div class="col-md-12">
				<h3>Members</h3>
				<table class="table table-striped">
					<thead>
					<tr>
						<td><b>User Name</b></td>
						<td><b>Score</b></td>
					</tr>
					</thead>
					<tbody>
					{% for member in team.members %}
						<tr>
							<td>
								<a href="{{ url_for('users.public', user_id=member.id) }}">
									{{ member.name }}
								</a>
							</td>
							<td>{{ member.score }}</td>
						</tr>
					{% endfor %}
					</tbody>
				</table>
			</div>
		</div>

		{% if solves or awards %}
			<div class="row">
				<div class="col-md-6 d-none d-md-block d-lg-block">
					<div id="keys-pie-graph" class="d-flex align-items-center">
						<div class="text-center w-100">
							<i class="fas fa-circle-notch fa-spin fa-3x fa-fw spinner"></i>
						</div>
					</div>
				</div>
				<div class="col-md-6 d-none d-md-block d-lg-block">
					<div id="categories-pie-graph" class="d-flex align-items-center">
						<div class="text-center w-100">
							<i class="fas fa-circle-notch fa-spin fa-3x fa-fw spinner"></i>
						</div>
					</div>
				</div>
				<br class="clearfix">
				<div class="col-md-12 d-none d-md-block d-lg-block">
					<div id="score-graph" class="w-100 d-flex align-items-center">
						<div class="text-center w-100">
							<i class="fas fa-circle-notch fa-spin fa-3x fa-fw spinner"></i>
						</div>
					</div>
				</div>
			</div>

			<div class="clearfix"></div>

			{% if awards %}
				<div class="row">
					<div class="col-md-12">
						<h3>Awards</h3>
					</div>
					{% for award in awards %}
						<div class="col-md-3 col-sm-6">
							<p class="text-center">
								<i class="award-icon award-{{ award.icon }} fa-2x"></i>
								<br>
								<strong>{{ award.name }}</strong>
							</p>
							{% if award.category %}<p class="text-center">{{ award.category }}</p>{% endif %}
							{% if award.description %}<p class="text-center">{{ award.description }}</p>{% endif %}
							<p class="text-center">{{ award.value }}</p>
						</div>
					{% endfor %}
				</div>

				<br>
			{% endif %}

			<div class="row">
				<div class="col-md-12">
					<h3>Solves</h3>
					<table class="table table-striped">
						<thead>
						<tr>
							<td><b>Challenge</b></td>
							<td class="d-none d-md-block d-lg-block"><b>Category</b></td>
							<td><b>Value</b></td>
							<td><b>Time</b></td>
						</tr>
						</thead>
						<tbody>
						{% for solve in solves %}
							<tr>
								<td>
									<a href="{{ url_for('challenges.listing') }}#{{ solve.challenge.name }}-{{ solve.challenge.id }}">
										{{ solve.challenge.name }}
									</a>
								</td>
								<td class="d-none d-md-block d-lg-block">{{ solve.challenge.category }}</td>
								<td>{{ solve.challenge.value }}</td>
								<td class="solve-time">
									<span data-time="{{ solve.date | isoformat }}"></span>
								</td>
							</tr>
						{% endfor %}
						</tbody>
					</table>
				</div>
			</div>
		{% else %}
			<div class="row min-vh-25">
				<h3 class="opacity-50 text-center w-100 justify-content-center align-self-center">
					No solves yet
				</h3>
			</div>
		{% endif %}
	</div>
{% endblock %}

{% block scripts %}
	<script>
		var stats_data = {{ {
			'type': 'team',
			'id': team.id,
			'name': team.name,
			'account_id': team.id,
		} | tojson }};
	</script>
	<script defer src="{{ url_for('views.themes', path='js/echarts.bundle.js') }}"></script>
	<script defer src="{{ url_for('views.themes', path='js/graphs.js') }}"></script>
{% endblock %}

{% block entrypoint %}
	{% if solves or awards %}
		<script defer src="{{ url_for('views.themes', path='js/pages/stats.js') }}"></script>
	{% endif %}
{% endblock %}

!function(d){function e(e){for(var t,i,o=e[0],n=e[1],r=e[2],a=0,s=[];a<o.length;a++)i=o[a],c[i]&&s.push(c[i][0]),c[i]=0;for(t in n)Object.prototype.hasOwnProperty.call(n,t)&&(d[t]=n[t]);for(u&&u(e);s.length;)s.shift()();return l.push.apply(l,r||[]),p()}function p(){for(var e,t=0;t<l.length;t++){for(var i=l[t],o=!0,n=1;n<i.length;n++){var r=i[n];0!==c[r]&&(o=!1)}o&&(l.splice(t--,1),e=a(a.s=i[0]))}return e}var i={},c={5:0,4:0},l=[];function a(e){if(i[e])return i[e].exports;var t=i[e]={i:e,l:!1,exports:{}};return d[e].call(t.exports,t,t.exports,a),t.l=!0,t.exports}a.m=d,a.c=i,a.d=function(e,t,i){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(t,e){if(1&e&&(t=a(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(a.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)a.d(i,o,function(e){return t[e]}.bind(null,o));return i},a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p="/themes/core/static/js";var t=window.webpackJsonp=window.webpackJsonp||[],o=t.push.bind(t);t.push=e,t=t.slice();for(var n=0;n<t.length;n++)e(t[n]);var u=o;l.push(["./CTFd/themes/core/assets/js/pages/notifications.js",0,1]),p()}({"./CTFd/themes/core/assets/js/CTFd.js":function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=l(i("./node_modules/jquery/dist/jquery.js")),n=l(i("./node_modules/dayjs/dayjs.min.js")),r=l(i("./node_modules/markdown-it/index.js"));i("./CTFd/themes/core/assets/js/patch.js");var a=l(i("./CTFd/themes/core/assets/js/fetch.js")),s=l(i("./CTFd/themes/core/assets/js/config.js")),d=i("./CTFd/themes/core/assets/js/api.js"),p=l(i("./CTFd/themes/core/assets/js/ezq.js")),c=i("./CTFd/themes/core/assets/js/utils.js");function l(e){return e&&e.__esModule?e:{default:e}}function u(t,e){var i,o=Object.keys(t);return Object.getOwnPropertySymbols&&(i=Object.getOwnPropertySymbols(t),e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),o.push.apply(o,i)),o}function f(n){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u(Object(r),!0).forEach(function(e){var t,i,o;t=n,o=r[i=e],i in t?Object.defineProperty(t,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(e){Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(r,e))})}return n}var m=new d.API("/"),h={},g={ezq:p.default},v={$:o.default,markdown:function(e){var t=f(f({},{html:!0,linkify:!0}),e),i=(0,r.default)(t);return i.renderer.rules.link_open=function(e,t,i,o,n){return e[t].attrPush(["target","_blank"]),n.renderToken(e,t,i)},i},dayjs:n.default},j=!1,y={run:function(e){e(T)}};var _={ajax:{getScript:c.getScript},html:{createHtmlNode:c.createHtmlNode,htmlEntities:c.htmlEntities}},T={init:function(e){j||(j=!0,s.default.urlRoot=e.urlRoot||s.default.urlRoot,s.default.csrfNonce=e.csrfNonce||s.default.csrfNonce,s.default.userMode=e.userMode||s.default.userMode,m.domain=s.default.urlRoot+"/api/v1",h.id=e.userId)},config:s.default,fetch:a.default,user:h,ui:g,utils:_,api:m,lib:v,_internal:{},plugin:y};t.default=T},"./CTFd/themes/core/assets/js/api.js":function(e,t,i){var c=o(i("./CTFd/themes/core/assets/js/fetch.js")),s=o(i("./node_modules/q/q.js"));function o(e){return e&&e.__esModule?e:{default:e}}function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var r=function(){"use strict";function e(e){var t="object"===n(e)?e.domain:e;if(this.domain=t||"",0===this.domain.length)throw new Error("Domain parameter must be specified as a string.")}function a(i,o){return i.$queryParameters&&Object.keys(i.$queryParameters).forEach(function(e){var t=i.$queryParameters[e];o[e]=t}),o}return e.prototype.request=function(e,t,i,o,n,r,a,s){var d=r&&Object.keys(r).length?function(e){var t,i=[];for(t in e)e.hasOwnProperty(t)&&i.push(encodeURIComponent(t)+"="+encodeURIComponent(e[t]));return i.join("&")}(r):null,p=t+(d?"?"+d:"");o&&!Object.keys(o).length&&(o=void 0),(0,c.default)(p,{method:e,headers:n,body:JSON.stringify(o)}).then(function(e){return e.json()}).then(function(e){s.resolve(e)}).catch(function(e){s.reject(e)})},e.prototype.post_award_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("POST",i+"/awards",e,{},n,o,{},t),t.promise},e.prototype.delete_award=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/awards/{award_id}".replace("{award_id}",e.awardId),void 0===e.awardId?i.reject(new Error("Missing required  parameter: awardId")):(n=a(e,n),this.request("DELETE",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_award=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/awards/{award_id}".replace("{award_id}",e.awardId),void 0===e.awardId?i.reject(new Error("Missing required  parameter: awardId")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.post_challenge_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("POST",i+"/challenges",e,{},n,o,{},t),t.promise},e.prototype.get_challenge_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("GET",i+"/challenges",e,{},n,o,{},t),t.promise},e.prototype.post_challenge_attempt=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("POST",i+"/challenges/attempt",e,{},n,o,{},t),t.promise},e.prototype.get_challenge_types=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("GET",i+"/challenges/types",e,{},n,o,{},t),t.promise},e.prototype.patch_challenge=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/challenges/{challenge_id}".replace("{challenge_id}",e.challengeId),void 0===e.challengeId?i.reject(new Error("Missing required  parameter: challengeId")):(n=a(e,n),this.request("PATCH",o+t,e,{},r,n,{},i)),i.promise},e.prototype.delete_challenge=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/challenges/{challenge_id}".replace("{challenge_id}",e.challengeId),void 0===e.challengeId?i.reject(new Error("Missing required  parameter: challengeId")):(n=a(e,n),this.request("DELETE",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_challenge=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/challenges/{challenge_id}".replace("{challenge_id}",e.challengeId),void 0===e.challengeId?i.reject(new Error("Missing required  parameter: challengeId")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_challenge_files=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],void 0!==e.id&&(n.id=e.id),t="/challenges/{challenge_id}/files".replace("{challenge_id}",e.challengeId),void 0===e.challengeId?i.reject(new Error("Missing required  parameter: challengeId")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_challenge_flags=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],void 0!==e.id&&(n.id=e.id),t="/challenges/{challenge_id}/flags".replace("{challenge_id}",e.challengeId),void 0===e.challengeId?i.reject(new Error("Missing required  parameter: challengeId")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_challenge_hints=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],void 0!==e.id&&(n.id=e.id),t="/challenges/{challenge_id}/hints".replace("{challenge_id}",e.challengeId),void 0===e.challengeId?i.reject(new Error("Missing required  parameter: challengeId")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_challenge_solves=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],void 0!==e.id&&(n.id=e.id),t="/challenges/{challenge_id}/solves".replace("{challenge_id}",e.challengeId),void 0===e.challengeId?i.reject(new Error("Missing required  parameter: challengeId")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_challenge_tags=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],void 0!==e.id&&(n.id=e.id),t="/challenges/{challenge_id}/tags".replace("{challenge_id}",e.challengeId),void 0===e.challengeId?i.reject(new Error("Missing required  parameter: challengeId")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.post_config_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("POST",i+"/configs",e,{},n,o,{},t),t.promise},e.prototype.patch_config_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("PATCH",i+"/configs",e,{},n,o,{},t),t.promise},e.prototype.get_config_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("GET",i+"/configs",e,{},n,o,{},t),t.promise},e.prototype.patch_config=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/configs/{config_key}".replace("{config_key}",e.configKey),void 0===e.configKey?i.reject(new Error("Missing required  parameter: configKey")):(n=a(e,n),this.request("PATCH",o+t,e,{},r,n,{},i)),i.promise},e.prototype.delete_config=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/configs/{config_key}".replace("{config_key}",e.configKey),void 0===e.configKey?i.reject(new Error("Missing required  parameter: configKey")):(n=a(e,n),this.request("DELETE",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_config=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/configs/{config_key}".replace("{config_key}",e.configKey),void 0===e.configKey?i.reject(new Error("Missing required  parameter: configKey")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.post_files_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("POST",i+"/files",e,{},n,o,{},t),t.promise},e.prototype.get_files_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("GET",i+"/files",e,{},n,o,{},t),t.promise},e.prototype.delete_files_detail=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/files/{file_id}".replace("{file_id}",e.fileId),void 0===e.fileId?i.reject(new Error("Missing required  parameter: fileId")):(n=a(e,n),this.request("DELETE",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_files_detail=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/files/{file_id}".replace("{file_id}",e.fileId),void 0===e.fileId?i.reject(new Error("Missing required  parameter: fileId")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.post_flag_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("POST",i+"/flags",e,{},n,o,{},t),t.promise},e.prototype.get_flag_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("GET",i+"/flags",e,{},n,o,{},t),t.promise},e.prototype.get_flag_types=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("GET",i+"/flags/types",e,{},n,o,{},t),t.promise},e.prototype.get_flag_types_1=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/flags/types/{type_name}".replace("{type_name}",e.typeName),void 0===e.typeName?i.reject(new Error("Missing required  parameter: typeName")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.patch_flag=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/flags/{flag_id}".replace("{flag_id}",e.flagId),void 0===e.flagId?i.reject(new Error("Missing required  parameter: flagId")):(n=a(e,n),this.request("PATCH",o+t,e,{},r,n,{},i)),i.promise},e.prototype.delete_flag=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/flags/{flag_id}".replace("{flag_id}",e.flagId),void 0===e.flagId?i.reject(new Error("Missing required  parameter: flagId")):(n=a(e,n),this.request("DELETE",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_flag=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/flags/{flag_id}".replace("{flag_id}",e.flagId),void 0===e.flagId?i.reject(new Error("Missing required  parameter: flagId")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.post_hint_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("POST",i+"/hints",e,{},n,o,{},t),t.promise},e.prototype.get_hint_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("GET",i+"/hints",e,{},n,o,{},t),t.promise},e.prototype.patch_hint=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/hints/{hint_id}".replace("{hint_id}",e.hintId),void 0===e.hintId?i.reject(new Error("Missing required  parameter: hintId")):(n=a(e,n),this.request("PATCH",o+t,e,{},r,n,{},i)),i.promise},e.prototype.delete_hint=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/hints/{hint_id}".replace("{hint_id}",e.hintId),void 0===e.hintId?i.reject(new Error("Missing required  parameter: hintId")):(n=a(e,n),this.request("DELETE",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_hint=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/hints/{hint_id}".replace("{hint_id}",e.hintId),void 0===e.hintId?i.reject(new Error("Missing required  parameter: hintId")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.post_notification_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("POST",i+"/notifications",e,{},n,o,{},t),t.promise},e.prototype.get_notification_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("GET",i+"/notifications",e,{},n,o,{},t),t.promise},e.prototype.delete_notification=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/notifications/{notification_id}".replace("{notification_id}",e.notificationId),void 0===e.notificationId?i.reject(new Error("Missing required  parameter: notificationId")):(n=a(e,n),this.request("DELETE",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_notification=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/notifications/{notification_id}".replace("{notification_id}",e.notificationId),void 0===e.notificationId?i.reject(new Error("Missing required  parameter: notificationId")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.post_page_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("POST",i+"/pages",e,{},n,o,{},t),t.promise},e.prototype.get_page_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("GET",i+"/pages",e,{},n,o,{},t),t.promise},e.prototype.patch_page_detail=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/pages/{page_id}".replace("{page_id}",e.pageId),void 0===e.pageId?i.reject(new Error("Missing required  parameter: pageId")):(n=a(e,n),this.request("PATCH",o+t,e,{},r,n,{},i)),i.promise},e.prototype.delete_page_detail=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/pages/{page_id}".replace("{page_id}",e.pageId),void 0===e.pageId?i.reject(new Error("Missing required  parameter: pageId")):(n=a(e,n),this.request("DELETE",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_page_detail=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/pages/{page_id}".replace("{page_id}",e.pageId),void 0===e.pageId?i.reject(new Error("Missing required  parameter: pageId")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_scoreboard_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("GET",i+"/scoreboard",e,{},n,o,{},t),t.promise},e.prototype.get_scoreboard_detail=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/scoreboard/top/{count}".replace("{count}",e.count),void 0===e.count?i.reject(new Error("Missing required  parameter: count")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_challenge_solve_statistics=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("GET",i+"/statistics/challenges/solves",e,{},n,o,{},t),t.promise},e.prototype.get_challenge_solve_percentages=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("GET",i+"/statistics/challenges/solves/percentages",e,{},n,o,{},t),t.promise},e.prototype.get_challenge_property_counts=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/statistics/challenges/{column}".replace("{column}",e.column),void 0===e.column?i.reject(new Error("Missing required  parameter: column")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_submission_property_counts=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/statistics/submissions/{column}".replace("{column}",e.column),void 0===e.column?i.reject(new Error("Missing required  parameter: column")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_team_statistics=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("GET",i+"/statistics/teams",e,{},n,o,{},t),t.promise},e.prototype.get_user_statistics=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("GET",i+"/statistics/users",e,{},n,o,{},t),t.promise},e.prototype.get_user_property_counts=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/statistics/users/{column}".replace("{column}",e.column),void 0===e.column?i.reject(new Error("Missing required  parameter: column")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.post_submissions_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("POST",i+"/submissions",e,{},n,o,{},t),t.promise},e.prototype.get_submissions_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("GET",i+"/submissions",e,{},n,o,{},t),t.promise},e.prototype.delete_submission=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/submissions/{submission_id}".replace("{submission_id}",e.submissionId),void 0===e.submissionId?i.reject(new Error("Missing required  parameter: submissionId")):(n=a(e,n),this.request("DELETE",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_submission=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/submissions/{submission_id}".replace("{submission_id}",e.submissionId),void 0===e.submissionId?i.reject(new Error("Missing required  parameter: submissionId")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.post_tag_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("POST",i+"/tags",e,{},n,o,{},t),t.promise},e.prototype.get_tag_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("GET",i+"/tags",e,{},n,o,{},t),t.promise},e.prototype.patch_tag=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/tags/{tag_id}".replace("{tag_id}",e.tagId),void 0===e.tagId?i.reject(new Error("Missing required  parameter: tagId")):(n=a(e,n),this.request("PATCH",o+t,e,{},r,n,{},i)),i.promise},e.prototype.delete_tag=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/tags/{tag_id}".replace("{tag_id}",e.tagId),void 0===e.tagId?i.reject(new Error("Missing required  parameter: tagId")):(n=a(e,n),this.request("DELETE",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_tag=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/tags/{tag_id}".replace("{tag_id}",e.tagId),void 0===e.tagId?i.reject(new Error("Missing required  parameter: tagId")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.post_team_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("POST",i+"/teams",e,{},n,o,{},t),t.promise},e.prototype.get_team_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("GET",i+"/teams",e,{},n,o,{},t),t.promise},e.prototype.patch_team_private=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],void 0!==e.teamId&&(o.team_id=e.teamId),o=a(e,o),this.request("PATCH",i+"/teams/me",e,{},n,o,{},t),t.promise},e.prototype.get_team_private=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],void 0!==e.teamId&&(o.team_id=e.teamId),o=a(e,o),this.request("GET",i+"/teams/me",e,{},n,o,{},t),t.promise},e.prototype.patch_team_public=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/teams/{team_id}".replace("{team_id}",e.teamId),void 0===e.teamId?i.reject(new Error("Missing required  parameter: teamId")):(n=a(e,n),this.request("PATCH",o+t,e,{},r,n,{},i)),i.promise},e.prototype.delete_team_public=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/teams/{team_id}".replace("{team_id}",e.teamId),void 0===e.teamId?i.reject(new Error("Missing required  parameter: teamId")):(n=a(e,n),this.request("DELETE",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_team_public=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/teams/{team_id}".replace("{team_id}",e.teamId),void 0===e.teamId?i.reject(new Error("Missing required  parameter: teamId")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_team_awards=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/teams/{team_id}/awards".replace("{team_id}",e.teamId),void 0===e.teamId?i.reject(new Error("Missing required  parameter: teamId")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_team_fails=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/teams/{team_id}/fails".replace("{team_id}",e.teamId),void 0===e.teamId?i.reject(new Error("Missing required  parameter: teamId")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_team_solves=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/teams/{team_id}/solves".replace("{team_id}",e.teamId),void 0===e.teamId?i.reject(new Error("Missing required  parameter: teamId")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.post_unlock_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("POST",i+"/unlocks",e,{},n,o,{},t),t.promise},e.prototype.get_unlock_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("GET",i+"/unlocks",e,{},n,o,{},t),t.promise},e.prototype.post_user_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("POST",i+"/users",e,{},n,o,{},t),t.promise},e.prototype.get_user_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("GET",i+"/users",e,{},n,o,{},t),t.promise},e.prototype.patch_user_private=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("PATCH",i+"/users/me",e,{},n,o,{},t),t.promise},e.prototype.get_user_private=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,o={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],o=a(e,o),this.request("GET",i+"/users/me",e,{},n,o,{},t),t.promise},e.prototype.patch_user_public=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/users/{user_id}".replace("{user_id}",e.userId),void 0===e.userId?i.reject(new Error("Missing required  parameter: userId")):(n=a(e,n),this.request("PATCH",o+t,e,{},r,n,{},i)),i.promise},e.prototype.delete_user_public=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/users/{user_id}".replace("{user_id}",e.userId),void 0===e.userId?i.reject(new Error("Missing required  parameter: userId")):(n=a(e,n),this.request("DELETE",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_user_public=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/users/{user_id}".replace("{user_id}",e.userId),void 0===e.userId?i.reject(new Error("Missing required  parameter: userId")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_user_awards=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/users/{user_id}/awards".replace("{user_id}",e.userId),void 0===e.userId?i.reject(new Error("Missing required  parameter: userId")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_user_fails=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/users/{user_id}/fails".replace("{user_id}",e.userId),void 0===e.userId?i.reject(new Error("Missing required  parameter: userId")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e.prototype.get_user_solves=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/users/{user_id}/solves".replace("{user_id}",e.userId),void 0===e.userId?i.reject(new Error("Missing required  parameter: userId")):(n=a(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise},e}();t.API=r},"./CTFd/themes/core/assets/js/config.js":function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={urlRoot:"",csrfNonce:"",userMode:""}},"./CTFd/themes/core/assets/js/events.js":function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=i("./node_modules/howler/dist/howler.js"),o=i("./node_modules/event-source-polyfill/src/eventsource.js"),a=i("./CTFd/themes/core/assets/js/ezq.js"),s=i("./CTFd/themes/core/assets/js/utils.js"),d=o.NativeEventSource||o.EventSourcePolyfill;t.default=function(e){var t=new d(e+"/events"),i=new s.WindowController,o=new r.Howl({src:[e+"/themes/core/static/sounds/notification.webm",e+"/themes/core/static/sounds/notification.mp3"]});function n(e){switch(e.type){case"toast":(0,s.inc_notification_counter)();var t=50<e.content.length?e.content.substring(0,47)+"...":e.content,i=!1;(0,a.ezToast)({title:e.title,body:t,onclick:function(){(0,a.ezAlert)({title:e.title,body:e.html,button:"Got it!",success:function(){i=!0,(0,s.dec_notification_counter)()}})},onclose:function(){i||(0,s.dec_notification_counter)()}});break;case"alert":(0,s.inc_notification_counter)(),(0,a.ezAlert)({title:e.title,body:e.html,button:"Got it!",success:function(){(0,s.dec_notification_counter)()}});break;case"background":default:(0,s.inc_notification_counter)()}}(0,s.init_notification_counter)(),i.alert=function(e){n(e)},i.toast=function(e){n(e)},i.background=function(e){n(e)},i.masterDidChange=function(){this.isMaster?t.addEventListener("notification",function(e){var t=JSON.parse(e.data);i.broadcast("notification",t),n(t),t.sound&&o.play()},!1):t&&t.close()}}},"./CTFd/themes/core/assets/js/ezq.js":function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.ezAlert=m,t.ezToast=h,t.ezQuery=g,t.ezProgressBar=v,t.ezBadge=j,t.default=void 0,i("./node_modules/bootstrap/js/dist/modal.js");var s=o(i("./node_modules/jquery/dist/jquery.js")),r=o(i("./node_modules/highlight.js/lib/index.js"));function o(e){return e&&e.__esModule?e:{default:e}}var a='<div class="modal fade" tabindex="-1" role="dialog">  <div class="modal-dialog" role="document">    <div class="modal-content">      <div class="modal-header">        <h5 class="modal-title">{0}</h5>        <button type="button" class="close" data-dismiss="modal" aria-label="Close">          <span aria-hidden="true">&times;</span>        </button>      </div>      <div class="modal-body">      </div>      <div class="modal-footer">      </div>    </div>  </div></div>',d='<div class="toast m-3" role="alert">  <div class="toast-header">    <strong class="mr-auto">{0}</strong>    <button type="button" class="ml-2 mb-1 close" data-dismiss="toast" aria-label="Close">      <span aria-hidden="true">&times;</span>    </button>  </div>  <div class="toast-body">{1}</div></div>',p='<div class="progress">  <div class="progress-bar progress-bar-success progress-bar-striped progress-bar-animated" role="progressbar" style="width: {0}%">  </div></div>',n='<div class="alert alert-danger alert-dismissable" role="alert">\n  <span class="sr-only">Error:</span>\n  {0}\n  <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>\n</div>',c='<div class="alert alert-success alert-dismissable submit-row" role="alert">\n  <strong>Success!</strong>\n  {0}\n  <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>\n</div>',l='<button type="button" class="btn btn-primary" data-dismiss="modal">{0}</button>',u='<button type="button" class="btn btn-danger" data-dismiss="modal">No</button>',f='<button type="button" class="btn btn-primary" data-dismiss="modal">Yes</button>';function m(e){var t=a.format(e.title),i=(0,s.default)(t);"string"==typeof e.body?i.find(".modal-body").append("<p>".concat(e.body,"</p>")):i.find(".modal-body").append((0,s.default)(e.body));var o=(0,s.default)(l.format(e.button));return e.success&&(0,s.default)(o).click(function(){e.success()}),e.large&&i.find(".modal-dialog").addClass("modal-lg"),i.find(".modal-footer").append(o),i.find("pre code").each(function(e){r.default.highlightBlock(this)}),(0,s.default)("main").append(i),i.modal("show"),(0,s.default)(i).on("hidden.bs.modal",function(){(0,s.default)(this).modal("dispose")}),i}function h(e){(0,s.default)("#ezq--notifications-toast-container").length||(0,s.default)("body").append((0,s.default)("<div/>").attr({id:"ezq--notifications-toast-container"}).css({position:"fixed",bottom:"0",right:"0","min-width":"20%"}));var t,i=d.format(e.title,e.body),o=(0,s.default)(i);e.onclose&&(0,s.default)(o).find("button[data-dismiss=toast]").click(function(){e.onclose()}),e.onclick&&((t=(0,s.default)(o).find(".toast-body")).addClass("cursor-pointer"),t.click(function(){e.onclick()}));var n=!1!==e.autohide,r=!1!==e.animation,a=e.delay||1e4;return(0,s.default)("#ezq--notifications-toast-container").prepend(o),o.toast({autohide:n,delay:a,animation:r}),o.toast("show"),o}function g(e){var t=a.format(e.title),i=(0,s.default)(t);"string"==typeof e.body?i.find(".modal-body").append("<p>".concat(e.body,"</p>")):i.find(".modal-body").append((0,s.default)(e.body));var o=(0,s.default)(f),n=(0,s.default)(u);return i.find(".modal-footer").append(n),i.find(".modal-footer").append(o),i.find("pre code").each(function(e){r.default.highlightBlock(this)}),(0,s.default)("main").append(i),(0,s.default)(i).on("hidden.bs.modal",function(){(0,s.default)(this).modal("dispose")}),(0,s.default)(o).click(function(){e.success()}),i.modal("show"),i}function v(e){if(e.target){var t=(0,s.default)(e.target);return t.find(".progress-bar").css("width",e.width+"%"),t}var i=p.format(e.width),o=a.format(e.title),n=(0,s.default)(o);return n.find(".modal-body").append((0,s.default)(i)),(0,s.default)("main").append(n),n.modal("show")}function j(e){var t={success:c,error:n}[e.type].format(e.body);return(0,s.default)(t)}var y={ezAlert:m,ezToast:h,ezQuery:g,ezProgressBar:v,ezBadge:j};t.default=y},"./CTFd/themes/core/assets/js/fetch.js":function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("./node_modules/whatwg-fetch/fetch.js");var o,n=(o=i("./CTFd/themes/core/assets/js/config.js"))&&o.__esModule?o:{default:o};var r=window.fetch;t.default=function(e,t){return void 0===t&&(t={method:"GET",credentials:"same-origin",headers:{}}),e=n.default.urlRoot+e,void 0===t.headers&&(t.headers={}),t.credentials="same-origin",t.headers.Accept="application/json",t.headers["Content-Type"]="application/json",t.headers["CSRF-Token"]=n.default.csrfNonce,r(e,t)}},"./CTFd/themes/core/assets/js/pages/main.js":function(e,t,i){var o=m(i("./CTFd/themes/core/assets/js/CTFd.js")),n=m(i("./node_modules/jquery/dist/jquery.js")),r=m(i("./node_modules/dayjs/dayjs.min.js")),a=m(i("./node_modules/dayjs/plugin/advancedFormat.js")),s=m(i("./node_modules/nunjucks/browser/nunjucks.js")),d=i("./node_modules/howler/dist/howler.js"),p=m(i("./CTFd/themes/core/assets/js/events.js")),c=m(i("./CTFd/themes/core/assets/js/config.js")),l=m(i("./CTFd/themes/core/assets/js/styles.js")),u=m(i("./CTFd/themes/core/assets/js/times.js")),f=m(i("./CTFd/themes/core/assets/js/helpers.js"));function m(e){return e&&e.__esModule?e:{default:e}}r.default.extend(a.default),o.default.init(window.init),window.CTFd=o.default,window.helpers=f.default,window.$=n.default,window.dayjs=r.default,window.nunjucks=s.default,window.Howl=d.Howl,(0,n.default)(function(){(0,l.default)(),(0,u.default)(),(0,p.default)(c.default.urlRoot)})},"./CTFd/themes/core/assets/js/pages/notifications.js":function(e,t,i){i("./CTFd/themes/core/assets/js/pages/main.js");var o,n=(o=i("./node_modules/jquery/dist/jquery.js"))&&o.__esModule?o:{default:o},r=i("./CTFd/themes/core/assets/js/utils.js");(0,n.default)(function(){(0,r.clear_notification_counter)()})},"./CTFd/themes/core/assets/js/patch.js":function(e,t,i){var o,s=(o=i("./node_modules/q/q.js"))&&o.__esModule?o:{default:o},n=i("./CTFd/themes/core/assets/js/api.js");function a(t,e){var i,o=Object.keys(t);return Object.getOwnPropertySymbols&&(i=Object.getOwnPropertySymbols(t),e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),o.push.apply(o,i)),o}function r(n){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?a(Object(r),!0).forEach(function(e){var t,i,o;t=n,o=r[i=e],i in t?Object.defineProperty(t,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(e){Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(r,e))})}return n}function d(e,t){return r(r({},e),t)}n.API.prototype.requestRaw=function(e,t,i,o,n,r,a,s){var d=r&&Object.keys(r).length?function(e){var t,i=[];for(t in e)e.hasOwnProperty(t)&&i.push(encodeURIComponent(t)+"="+encodeURIComponent(e[t]));return i.join("&")}(r):null,p=t+(d?"?"+d:"");o&&!Object.keys(o).length&&(o=void 0),fetch(p,{method:e,headers:n,body:o}).then(function(e){return e.json()}).then(function(e){s.resolve(e)}).catch(function(e){s.reject(e)})},n.API.prototype.patch_user_public=function(e,t){void 0===e&&(e={});var i,o=s.default.defer(),n=this.domain,r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],i="/users/{user_id}".replace("{user_id}",e.userId),void 0===e.userId?o.reject(new Error("Missing required  parameter: userId")):this.request("PATCH",n+i,e,t,r,{},{},o),o.promise},n.API.prototype.patch_user_private=function(e,t){void 0===e&&(e={});var i=s.default.defer(),o=this.domain,n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],this.request("PATCH",o+"/users/me",e,t,n,{},{},i),i.promise},n.API.prototype.post_unlock_list=function(e,t){var i=s.default.defer(),o=this.domain,n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],this.request("POST",o+"/unlocks",e,t,n,{},{},i),i.promise},n.API.prototype.post_notification_list=function(e,t){void 0===e&&(e={});var i=s.default.defer(),o=this.domain,n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],this.request("POST",o+"/notifications",e,t,n,{},{},i),i.promise},n.API.prototype.post_files_list=function(e,t){var i=s.default.defer(),o=this.domain,n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],this.requestRaw("POST",o+"/files",e,t,n,{},{},i),i.promise},n.API.prototype.patch_config=function(e,t){void 0===e&&(e={});var i,o=s.default.defer(),n=this.domain,r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],i="/configs/{config_key}".replace("{config_key}",e.configKey),void 0===e.configKey?o.reject(new Error("Missing required  parameter: configKey")):this.request("PATCH",n+i,e,t,r,{},{},o),o.promise},n.API.prototype.patch_config_list=function(e,t){void 0===e&&(e={});var i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],n=d(e,n),this.request("PATCH",o+"/configs",e,t,r,n,{},i),i.promise},n.API.prototype.post_tag_list=function(e,t){void 0===e&&(e={});var i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],n=d(e,n),this.request("POST",o+"/tags",e,t,r,n,{},i),i.promise},n.API.prototype.patch_team_public=function(e,t){void 0===e&&(e={});var i,o=s.default.defer(),n=this.domain,r={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],i="/teams/{team_id}".replace("{team_id}",e.teamId),void 0===e.teamId?o.reject(new Error("Missing required  parameter: teamId")):(r=d(e,r),this.request("PATCH",n+i,e,t,a,r,{},o)),o.promise},n.API.prototype.post_challenge_attempt=function(e,t){void 0===e&&(e={});var i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],n=d(e,n),this.request("POST",o+"/challenges/attempt",e,t,r,n,{},i),i.promise},n.API.prototype.get_hint=function(e){void 0===e&&(e={});var t,i=s.default.defer(),o=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/hints/{hint_id}".replace("{hint_id}",e.hintId),void 0===e.hintId?i.reject(new Error("Missing required  parameter: hintId")):(delete e.hintId,n=d(e,n),this.request("GET",o+t,e,{},r,n,{},i)),i.promise}},"./CTFd/themes/core/assets/js/styles.js":function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("./node_modules/bootstrap/dist/js/bootstrap.bundle.js");var o=r(i("./node_modules/jquery/dist/jquery.js")),n=r(i("./node_modules/highlight.js/lib/index.js"));function r(e){return e&&e.__esModule?e:{default:e}}t.default=function(){(0,o.default)(":input").each(function(){(0,o.default)(this).data("initial",(0,o.default)(this).val())}),(0,o.default)(".form-control").bind({focus:function(){(0,o.default)(this).removeClass("input-filled-invalid"),(0,o.default)(this).addClass("input-filled-valid")},blur:function(){""===(0,o.default)(this).val()&&((0,o.default)(this).removeClass("input-filled-invalid"),(0,o.default)(this).removeClass("input-filled-valid"))}}),(0,o.default)(".form-control").each(function(){(0,o.default)(this).val()&&(0,o.default)(this).addClass("input-filled-valid")}),(0,o.default)(".page-select").change(function(){var e=new URL(window.location);e.searchParams.set("page",this.value),window.location.href=e.toString()}),(0,o.default)('[data-toggle="tooltip"]').tooltip(),(0,o.default)(function(){document.querySelectorAll("pre code").forEach(function(e){n.default.highlightBlock(e)})})}},"./CTFd/themes/core/assets/js/times.js":function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(i("./node_modules/dayjs/dayjs.min.js")),o=n(i("./node_modules/dayjs/plugin/advancedFormat.js")),a=n(i("./node_modules/jquery/dist/jquery.js"));function n(e){return e&&e.__esModule?e:{default:e}}r.default.extend(o.default);t.default=function(){(0,a.default)("[data-time]").each(function(e,t){var i=(0,a.default)(t),o=i.data("time"),n=i.data("time-format")||"MMMM Do, h:mm:ss A";t.innerText=(0,r.default)(o).format(n)})}},"./CTFd/themes/core/assets/js/utils.js":function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.WindowController=n,t.colorHash=function(e){for(var t=0,i=0;i<e.length;i++)t=e.charCodeAt(i)+((t<<5)-t),t&=t;var o=(t%25+25)%25+75,n=(t%20+20)%20+40;return"hsl(".concat((t%360+360)%360,", ").concat(o,"%, ").concat(n,"%)")},t.cumulativeSum=function(e){for(var t=e.concat(),i=0;i<e.length;i++)t[i]=e.slice(0,i+1).reduce(function(e,t){return e+t});return t},t.init_notification_counter=function(){var e=r.getItem(s);null===e?r.setItem(s,0):0<e&&(0,a.default)(".badge-notification").text(e)},t.set_notification_counter=function(e){r.setItem(s,e)},t.inc_notification_counter=function(){var e=r.getItem(s)||0;r.setItem(s,++e),(0,a.default)(".badge-notification").text(e)},t.dec_notification_counter=function(){var e=r.getItem(s)||0;0<e&&(r.setItem(s,--e),(0,a.default)(".badge-notification").text(e));0==e&&d()},t.clear_notification_counter=d,t.copyToClipboard=function(e,t){(0,a.default)(t).select(),document.execCommand("copy"),(0,a.default)(e.target).tooltip({title:"Copied!",trigger:"manual"}),(0,a.default)(e.target).tooltip("show"),setTimeout(function(){(0,a.default)(e.target).tooltip("hide")},1500)},t.makeSortableTables=function(){function r(e,t){return(0,a.default)(e).children("td").eq(t).text()}(0,a.default)("th.sort-col").append(' <i class="fas fa-sort"></i>'),(0,a.default)("th.sort-col").click(function(){var n,e=(0,a.default)(this).parents("table").eq(0),t=e.find("tr:gt(0)").toArray().sort((n=(0,a.default)(this).index(),function(e,t){var i=r(e,n),o=r(t,n);return a.default.isNumeric(i)&&a.default.isNumeric(o)?i-o:i.toString().localeCompare(o)}));this.asc=!this.asc,this.asc||(t=t.reverse());for(var i=0;i<t.length;i++)e.append(t[i])})},t.getScript=function(o){return new Promise(function(e,t){var i=document.createElement("script");document.body.appendChild(i),i.onload=e,i.onerror=t,i.async=!0,i.src=o})},t.createHtmlNode=function(e){var t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild},t.htmlEntities=function(e){return(0,a.default)("<div/>").text(e).html()};var o,a=(o=i("./node_modules/jquery/dist/jquery.js"))&&o.__esModule?o:{default:o};function n(){this.id=Math.random(),this.isMaster=!1,this.others={},window.addEventListener("storage",this,!1),window.addEventListener("unload",this,!1),this.broadcast("hello");var t=this;this._checkTimeout=setTimeout(function e(){t.check(),t._checkTimeout=setTimeout(e,9e3)},500),this._pingTimeout=setTimeout(function e(){t.sendPing(),t._pingTimeout=setTimeout(e,17e3)},17e3)}a.default.fn.serializeJSON=function(i){var o={},n=(0,a.default)(this),e=n.serializeArray();return(e=(e=e.concat(n.find("input[type=checkbox]:checked").map(function(){return{name:this.name,value:!0}}).get())).concat(n.find("input[type=checkbox]:not(:checked)").map(function(){return{name:this.name,value:!1}}).get())).map(function(e){var t;i&&(null===e.value||""===e.value)&&(t=n.find(":input[name='".concat(e.name,"']"))).data("initial")===t.val()||(o[e.name]=e.value)}),o},String.prototype.format=String.prototype.f=function(){for(var e=this,t=arguments.length;t--;)e=e.replace(new RegExp("\\{"+t+"\\}","gm"),arguments[t]);return e},String.prototype.hashCode=function(){var e,t,i=0;if(0==this.length)return i;for(e=0,t=this.length;e<t;e++)i=(i<<5)-i+this.charCodeAt(e),i|=0;return i},n.prototype.destroy=function(){clearTimeout(this._pingTimeout),clearTimeout(this._checkTimeout),window.removeEventListener("storage",this,!1),window.removeEventListener("unload",this,!1),this.broadcast("bye")},n.prototype.handleEvent=function(e){if("unload"===e.type)this.destroy();else if("broadcast"===e.key)try{var t=JSON.parse(e.newValue);t.id!==this.id&&this[t.type](t)}catch(e){}},n.prototype.sendPing=function(){this.broadcast("ping")},n.prototype.hello=function(e){this.ping(e),e.id<this.id?this.check():this.sendPing()},n.prototype.ping=function(e){this.others[e.id]=+new Date},n.prototype.bye=function(e){delete this.others[e.id],this.check()},n.prototype.check=function(e){var t,i=+new Date,o=!0;for(t in this.others)this.others[t]+23e3<i?delete this.others[t]:t<this.id&&(o=!1);this.isMaster!==o&&(this.isMaster=o,this.masterDidChange())},n.prototype.masterDidChange=function(){},n.prototype.broadcast=function(e,t){var i,o={id:this.id,type:e};for(i in t)o[i]=t[i];try{localStorage.setItem("broadcast",JSON.stringify(o))}catch(e){}};var r=window.localStorage,s="unread_notifications";function d(){r.setItem(s,0),(0,a.default)(".badge-notification").empty()}},0:function(e,t){},1:function(e,t){}});
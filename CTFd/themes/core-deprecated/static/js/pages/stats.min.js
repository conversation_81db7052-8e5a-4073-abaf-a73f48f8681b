!function(d){function e(e){for(var t,o,i=e[0],n=e[1],r=e[2],a=0,s=[];a<i.length;a++)o=i[a],p[o]&&s.push(p[o][0]),p[o]=0;for(t in n)Object.prototype.hasOwnProperty.call(n,t)&&(d[t]=n[t]);for(u&&u(e);s.length;)s.shift()();return l.push.apply(l,r||[]),c()}function c(){for(var e,t=0;t<l.length;t++){for(var o=l[t],i=!0,n=1;n<o.length;n++){var r=o[n];0!==p[r]&&(i=!1)}i&&(l.splice(t--,1),e=a(a.s=o[0]))}return e}var o={},p={9:0,4:0},l=[];function a(e){if(o[e])return o[e].exports;var t=o[e]={i:e,l:!1,exports:{}};return d[e].call(t.exports,t,t.exports,a),t.l=!0,t.exports}a.m=d,a.c=o,a.d=function(e,t,o){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(t,e){if(1&e&&(t=a(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var o=Object.create(null);if(a.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)a.d(o,i,function(e){return t[e]}.bind(null,i));return o},a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p="/themes/core/static/js";var t=window.webpackJsonp=window.webpackJsonp||[],i=t.push.bind(t);t.push=e,t=t.slice();for(var n=0;n<t.length;n++)e(t[n]);var u=i;l.push(["./CTFd/themes/core/assets/js/pages/stats.js",0,2,1]),c()}({"./CTFd/themes/core/assets/js/CTFd.js":function(e,t,o){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=l(o("./node_modules/jquery/dist/jquery.js")),n=l(o("./node_modules/dayjs/dayjs.min.js")),r=l(o("./node_modules/markdown-it/index.js"));o("./CTFd/themes/core/assets/js/patch.js");var a=l(o("./CTFd/themes/core/assets/js/fetch.js")),s=l(o("./CTFd/themes/core/assets/js/config.js")),d=o("./CTFd/themes/core/assets/js/api.js"),c=l(o("./CTFd/themes/core/assets/js/ezq.js")),p=o("./CTFd/themes/core/assets/js/utils.js");function l(e){return e&&e.__esModule?e:{default:e}}function u(t,e){var o,i=Object.keys(t);return Object.getOwnPropertySymbols&&(o=Object.getOwnPropertySymbols(t),e&&(o=o.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,o)),i}function f(n){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u(Object(r),!0).forEach(function(e){var t,o,i;t=n,i=r[o=e],o in t?Object.defineProperty(t,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(e){Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(r,e))})}return n}var m=new d.API("/"),h={},g={ezq:c.default},v={$:i.default,markdown:function(e){var t=f(f({},{html:!0,linkify:!0}),e),o=(0,r.default)(t);return o.renderer.rules.link_open=function(e,t,o,i,n){return e[t].attrPush(["target","_blank"]),n.renderToken(e,t,o)},o},dayjs:n.default},y=!1,j={run:function(e){e(T)}};var _={ajax:{getScript:p.getScript},html:{createHtmlNode:p.createHtmlNode,htmlEntities:p.htmlEntities}},T={init:function(e){y||(y=!0,s.default.urlRoot=e.urlRoot||s.default.urlRoot,s.default.csrfNonce=e.csrfNonce||s.default.csrfNonce,s.default.userMode=e.userMode||s.default.userMode,m.domain=s.default.urlRoot+"/api/v1",h.id=e.userId)},config:s.default,fetch:a.default,user:h,ui:g,utils:_,api:m,lib:v,_internal:{},plugin:j};t.default=T},"./CTFd/themes/core/assets/js/api.js":function(e,t,o){var p=i(o("./CTFd/themes/core/assets/js/fetch.js")),s=i(o("./node_modules/q/q.js"));function i(e){return e&&e.__esModule?e:{default:e}}function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var r=function(){"use strict";function e(e){var t="object"===n(e)?e.domain:e;if(this.domain=t||"",0===this.domain.length)throw new Error("Domain parameter must be specified as a string.")}function a(o,i){return o.$queryParameters&&Object.keys(o.$queryParameters).forEach(function(e){var t=o.$queryParameters[e];i[e]=t}),i}return e.prototype.request=function(e,t,o,i,n,r,a,s){var d=r&&Object.keys(r).length?function(e){var t,o=[];for(t in e)e.hasOwnProperty(t)&&o.push(encodeURIComponent(t)+"="+encodeURIComponent(e[t]));return o.join("&")}(r):null,c=t+(d?"?"+d:"");i&&!Object.keys(i).length&&(i=void 0),(0,p.default)(c,{method:e,headers:n,body:JSON.stringify(i)}).then(function(e){return e.json()}).then(function(e){s.resolve(e)}).catch(function(e){s.reject(e)})},e.prototype.post_award_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("POST",o+"/awards",e,{},n,i,{},t),t.promise},e.prototype.delete_award=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/awards/{award_id}".replace("{award_id}",e.awardId),void 0===e.awardId?o.reject(new Error("Missing required  parameter: awardId")):(n=a(e,n),this.request("DELETE",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_award=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/awards/{award_id}".replace("{award_id}",e.awardId),void 0===e.awardId?o.reject(new Error("Missing required  parameter: awardId")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.post_challenge_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("POST",o+"/challenges",e,{},n,i,{},t),t.promise},e.prototype.get_challenge_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("GET",o+"/challenges",e,{},n,i,{},t),t.promise},e.prototype.post_challenge_attempt=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("POST",o+"/challenges/attempt",e,{},n,i,{},t),t.promise},e.prototype.get_challenge_types=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("GET",o+"/challenges/types",e,{},n,i,{},t),t.promise},e.prototype.patch_challenge=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/challenges/{challenge_id}".replace("{challenge_id}",e.challengeId),void 0===e.challengeId?o.reject(new Error("Missing required  parameter: challengeId")):(n=a(e,n),this.request("PATCH",i+t,e,{},r,n,{},o)),o.promise},e.prototype.delete_challenge=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/challenges/{challenge_id}".replace("{challenge_id}",e.challengeId),void 0===e.challengeId?o.reject(new Error("Missing required  parameter: challengeId")):(n=a(e,n),this.request("DELETE",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_challenge=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/challenges/{challenge_id}".replace("{challenge_id}",e.challengeId),void 0===e.challengeId?o.reject(new Error("Missing required  parameter: challengeId")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_challenge_files=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],void 0!==e.id&&(n.id=e.id),t="/challenges/{challenge_id}/files".replace("{challenge_id}",e.challengeId),void 0===e.challengeId?o.reject(new Error("Missing required  parameter: challengeId")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_challenge_flags=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],void 0!==e.id&&(n.id=e.id),t="/challenges/{challenge_id}/flags".replace("{challenge_id}",e.challengeId),void 0===e.challengeId?o.reject(new Error("Missing required  parameter: challengeId")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_challenge_hints=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],void 0!==e.id&&(n.id=e.id),t="/challenges/{challenge_id}/hints".replace("{challenge_id}",e.challengeId),void 0===e.challengeId?o.reject(new Error("Missing required  parameter: challengeId")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_challenge_solves=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],void 0!==e.id&&(n.id=e.id),t="/challenges/{challenge_id}/solves".replace("{challenge_id}",e.challengeId),void 0===e.challengeId?o.reject(new Error("Missing required  parameter: challengeId")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_challenge_tags=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],void 0!==e.id&&(n.id=e.id),t="/challenges/{challenge_id}/tags".replace("{challenge_id}",e.challengeId),void 0===e.challengeId?o.reject(new Error("Missing required  parameter: challengeId")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.post_config_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("POST",o+"/configs",e,{},n,i,{},t),t.promise},e.prototype.patch_config_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("PATCH",o+"/configs",e,{},n,i,{},t),t.promise},e.prototype.get_config_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("GET",o+"/configs",e,{},n,i,{},t),t.promise},e.prototype.patch_config=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/configs/{config_key}".replace("{config_key}",e.configKey),void 0===e.configKey?o.reject(new Error("Missing required  parameter: configKey")):(n=a(e,n),this.request("PATCH",i+t,e,{},r,n,{},o)),o.promise},e.prototype.delete_config=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/configs/{config_key}".replace("{config_key}",e.configKey),void 0===e.configKey?o.reject(new Error("Missing required  parameter: configKey")):(n=a(e,n),this.request("DELETE",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_config=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/configs/{config_key}".replace("{config_key}",e.configKey),void 0===e.configKey?o.reject(new Error("Missing required  parameter: configKey")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.post_files_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("POST",o+"/files",e,{},n,i,{},t),t.promise},e.prototype.get_files_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("GET",o+"/files",e,{},n,i,{},t),t.promise},e.prototype.delete_files_detail=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/files/{file_id}".replace("{file_id}",e.fileId),void 0===e.fileId?o.reject(new Error("Missing required  parameter: fileId")):(n=a(e,n),this.request("DELETE",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_files_detail=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/files/{file_id}".replace("{file_id}",e.fileId),void 0===e.fileId?o.reject(new Error("Missing required  parameter: fileId")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.post_flag_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("POST",o+"/flags",e,{},n,i,{},t),t.promise},e.prototype.get_flag_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("GET",o+"/flags",e,{},n,i,{},t),t.promise},e.prototype.get_flag_types=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("GET",o+"/flags/types",e,{},n,i,{},t),t.promise},e.prototype.get_flag_types_1=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/flags/types/{type_name}".replace("{type_name}",e.typeName),void 0===e.typeName?o.reject(new Error("Missing required  parameter: typeName")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.patch_flag=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/flags/{flag_id}".replace("{flag_id}",e.flagId),void 0===e.flagId?o.reject(new Error("Missing required  parameter: flagId")):(n=a(e,n),this.request("PATCH",i+t,e,{},r,n,{},o)),o.promise},e.prototype.delete_flag=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/flags/{flag_id}".replace("{flag_id}",e.flagId),void 0===e.flagId?o.reject(new Error("Missing required  parameter: flagId")):(n=a(e,n),this.request("DELETE",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_flag=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/flags/{flag_id}".replace("{flag_id}",e.flagId),void 0===e.flagId?o.reject(new Error("Missing required  parameter: flagId")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.post_hint_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("POST",o+"/hints",e,{},n,i,{},t),t.promise},e.prototype.get_hint_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("GET",o+"/hints",e,{},n,i,{},t),t.promise},e.prototype.patch_hint=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/hints/{hint_id}".replace("{hint_id}",e.hintId),void 0===e.hintId?o.reject(new Error("Missing required  parameter: hintId")):(n=a(e,n),this.request("PATCH",i+t,e,{},r,n,{},o)),o.promise},e.prototype.delete_hint=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/hints/{hint_id}".replace("{hint_id}",e.hintId),void 0===e.hintId?o.reject(new Error("Missing required  parameter: hintId")):(n=a(e,n),this.request("DELETE",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_hint=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/hints/{hint_id}".replace("{hint_id}",e.hintId),void 0===e.hintId?o.reject(new Error("Missing required  parameter: hintId")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.post_notification_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("POST",o+"/notifications",e,{},n,i,{},t),t.promise},e.prototype.get_notification_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("GET",o+"/notifications",e,{},n,i,{},t),t.promise},e.prototype.delete_notification=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/notifications/{notification_id}".replace("{notification_id}",e.notificationId),void 0===e.notificationId?o.reject(new Error("Missing required  parameter: notificationId")):(n=a(e,n),this.request("DELETE",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_notification=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/notifications/{notification_id}".replace("{notification_id}",e.notificationId),void 0===e.notificationId?o.reject(new Error("Missing required  parameter: notificationId")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.post_page_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("POST",o+"/pages",e,{},n,i,{},t),t.promise},e.prototype.get_page_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("GET",o+"/pages",e,{},n,i,{},t),t.promise},e.prototype.patch_page_detail=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/pages/{page_id}".replace("{page_id}",e.pageId),void 0===e.pageId?o.reject(new Error("Missing required  parameter: pageId")):(n=a(e,n),this.request("PATCH",i+t,e,{},r,n,{},o)),o.promise},e.prototype.delete_page_detail=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/pages/{page_id}".replace("{page_id}",e.pageId),void 0===e.pageId?o.reject(new Error("Missing required  parameter: pageId")):(n=a(e,n),this.request("DELETE",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_page_detail=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/pages/{page_id}".replace("{page_id}",e.pageId),void 0===e.pageId?o.reject(new Error("Missing required  parameter: pageId")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_scoreboard_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("GET",o+"/scoreboard",e,{},n,i,{},t),t.promise},e.prototype.get_scoreboard_detail=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/scoreboard/top/{count}".replace("{count}",e.count),void 0===e.count?o.reject(new Error("Missing required  parameter: count")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_challenge_solve_statistics=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("GET",o+"/statistics/challenges/solves",e,{},n,i,{},t),t.promise},e.prototype.get_challenge_solve_percentages=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("GET",o+"/statistics/challenges/solves/percentages",e,{},n,i,{},t),t.promise},e.prototype.get_challenge_property_counts=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/statistics/challenges/{column}".replace("{column}",e.column),void 0===e.column?o.reject(new Error("Missing required  parameter: column")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_submission_property_counts=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/statistics/submissions/{column}".replace("{column}",e.column),void 0===e.column?o.reject(new Error("Missing required  parameter: column")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_team_statistics=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("GET",o+"/statistics/teams",e,{},n,i,{},t),t.promise},e.prototype.get_user_statistics=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("GET",o+"/statistics/users",e,{},n,i,{},t),t.promise},e.prototype.get_user_property_counts=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/statistics/users/{column}".replace("{column}",e.column),void 0===e.column?o.reject(new Error("Missing required  parameter: column")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.post_submissions_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("POST",o+"/submissions",e,{},n,i,{},t),t.promise},e.prototype.get_submissions_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("GET",o+"/submissions",e,{},n,i,{},t),t.promise},e.prototype.delete_submission=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/submissions/{submission_id}".replace("{submission_id}",e.submissionId),void 0===e.submissionId?o.reject(new Error("Missing required  parameter: submissionId")):(n=a(e,n),this.request("DELETE",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_submission=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/submissions/{submission_id}".replace("{submission_id}",e.submissionId),void 0===e.submissionId?o.reject(new Error("Missing required  parameter: submissionId")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.post_tag_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("POST",o+"/tags",e,{},n,i,{},t),t.promise},e.prototype.get_tag_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("GET",o+"/tags",e,{},n,i,{},t),t.promise},e.prototype.patch_tag=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/tags/{tag_id}".replace("{tag_id}",e.tagId),void 0===e.tagId?o.reject(new Error("Missing required  parameter: tagId")):(n=a(e,n),this.request("PATCH",i+t,e,{},r,n,{},o)),o.promise},e.prototype.delete_tag=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/tags/{tag_id}".replace("{tag_id}",e.tagId),void 0===e.tagId?o.reject(new Error("Missing required  parameter: tagId")):(n=a(e,n),this.request("DELETE",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_tag=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/tags/{tag_id}".replace("{tag_id}",e.tagId),void 0===e.tagId?o.reject(new Error("Missing required  parameter: tagId")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.post_team_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("POST",o+"/teams",e,{},n,i,{},t),t.promise},e.prototype.get_team_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("GET",o+"/teams",e,{},n,i,{},t),t.promise},e.prototype.patch_team_private=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],void 0!==e.teamId&&(i.team_id=e.teamId),i=a(e,i),this.request("PATCH",o+"/teams/me",e,{},n,i,{},t),t.promise},e.prototype.get_team_private=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],void 0!==e.teamId&&(i.team_id=e.teamId),i=a(e,i),this.request("GET",o+"/teams/me",e,{},n,i,{},t),t.promise},e.prototype.patch_team_public=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/teams/{team_id}".replace("{team_id}",e.teamId),void 0===e.teamId?o.reject(new Error("Missing required  parameter: teamId")):(n=a(e,n),this.request("PATCH",i+t,e,{},r,n,{},o)),o.promise},e.prototype.delete_team_public=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/teams/{team_id}".replace("{team_id}",e.teamId),void 0===e.teamId?o.reject(new Error("Missing required  parameter: teamId")):(n=a(e,n),this.request("DELETE",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_team_public=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/teams/{team_id}".replace("{team_id}",e.teamId),void 0===e.teamId?o.reject(new Error("Missing required  parameter: teamId")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_team_awards=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/teams/{team_id}/awards".replace("{team_id}",e.teamId),void 0===e.teamId?o.reject(new Error("Missing required  parameter: teamId")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_team_fails=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/teams/{team_id}/fails".replace("{team_id}",e.teamId),void 0===e.teamId?o.reject(new Error("Missing required  parameter: teamId")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_team_solves=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/teams/{team_id}/solves".replace("{team_id}",e.teamId),void 0===e.teamId?o.reject(new Error("Missing required  parameter: teamId")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.post_unlock_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("POST",o+"/unlocks",e,{},n,i,{},t),t.promise},e.prototype.get_unlock_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("GET",o+"/unlocks",e,{},n,i,{},t),t.promise},e.prototype.post_user_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("POST",o+"/users",e,{},n,i,{},t),t.promise},e.prototype.get_user_list=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("GET",o+"/users",e,{},n,i,{},t),t.promise},e.prototype.patch_user_private=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("PATCH",o+"/users/me",e,{},n,i,{},t),t.promise},e.prototype.get_user_private=function(e){void 0===e&&(e={});var t=s.default.defer(),o=this.domain,i={},n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],i=a(e,i),this.request("GET",o+"/users/me",e,{},n,i,{},t),t.promise},e.prototype.patch_user_public=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/users/{user_id}".replace("{user_id}",e.userId),void 0===e.userId?o.reject(new Error("Missing required  parameter: userId")):(n=a(e,n),this.request("PATCH",i+t,e,{},r,n,{},o)),o.promise},e.prototype.delete_user_public=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/users/{user_id}".replace("{user_id}",e.userId),void 0===e.userId?o.reject(new Error("Missing required  parameter: userId")):(n=a(e,n),this.request("DELETE",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_user_public=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/users/{user_id}".replace("{user_id}",e.userId),void 0===e.userId?o.reject(new Error("Missing required  parameter: userId")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_user_awards=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/users/{user_id}/awards".replace("{user_id}",e.userId),void 0===e.userId?o.reject(new Error("Missing required  parameter: userId")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_user_fails=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/users/{user_id}/fails".replace("{user_id}",e.userId),void 0===e.userId?o.reject(new Error("Missing required  parameter: userId")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e.prototype.get_user_solves=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/users/{user_id}/solves".replace("{user_id}",e.userId),void 0===e.userId?o.reject(new Error("Missing required  parameter: userId")):(n=a(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise},e}();t.API=r},"./CTFd/themes/core/assets/js/config.js":function(e,t,o){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={urlRoot:"",csrfNonce:"",userMode:""}},"./CTFd/themes/core/assets/js/events.js":function(e,t,o){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=o("./node_modules/howler/dist/howler.js"),i=o("./node_modules/event-source-polyfill/src/eventsource.js"),a=o("./CTFd/themes/core/assets/js/ezq.js"),s=o("./CTFd/themes/core/assets/js/utils.js"),d=i.NativeEventSource||i.EventSourcePolyfill;t.default=function(e){var t=new d(e+"/events"),o=new s.WindowController,i=new r.Howl({src:[e+"/themes/core/static/sounds/notification.webm",e+"/themes/core/static/sounds/notification.mp3"]});function n(e){switch(e.type){case"toast":(0,s.inc_notification_counter)();var t=50<e.content.length?e.content.substring(0,47)+"...":e.content,o=!1;(0,a.ezToast)({title:e.title,body:t,onclick:function(){(0,a.ezAlert)({title:e.title,body:e.html,button:"Got it!",success:function(){o=!0,(0,s.dec_notification_counter)()}})},onclose:function(){o||(0,s.dec_notification_counter)()}});break;case"alert":(0,s.inc_notification_counter)(),(0,a.ezAlert)({title:e.title,body:e.html,button:"Got it!",success:function(){(0,s.dec_notification_counter)()}});break;case"background":default:(0,s.inc_notification_counter)()}}(0,s.init_notification_counter)(),o.alert=function(e){n(e)},o.toast=function(e){n(e)},o.background=function(e){n(e)},o.masterDidChange=function(){this.isMaster?t.addEventListener("notification",function(e){var t=JSON.parse(e.data);o.broadcast("notification",t),n(t),t.sound&&i.play()},!1):t&&t.close()}}},"./CTFd/themes/core/assets/js/ezq.js":function(e,t,o){Object.defineProperty(t,"__esModule",{value:!0}),t.ezAlert=m,t.ezToast=h,t.ezQuery=g,t.ezProgressBar=v,t.ezBadge=y,t.default=void 0,o("./node_modules/bootstrap/js/dist/modal.js");var s=i(o("./node_modules/jquery/dist/jquery.js")),r=i(o("./node_modules/highlight.js/lib/index.js"));function i(e){return e&&e.__esModule?e:{default:e}}var a='<div class="modal fade" tabindex="-1" role="dialog">  <div class="modal-dialog" role="document">    <div class="modal-content">      <div class="modal-header">        <h5 class="modal-title">{0}</h5>        <button type="button" class="close" data-dismiss="modal" aria-label="Close">          <span aria-hidden="true">&times;</span>        </button>      </div>      <div class="modal-body">      </div>      <div class="modal-footer">      </div>    </div>  </div></div>',d='<div class="toast m-3" role="alert">  <div class="toast-header">    <strong class="mr-auto">{0}</strong>    <button type="button" class="ml-2 mb-1 close" data-dismiss="toast" aria-label="Close">      <span aria-hidden="true">&times;</span>    </button>  </div>  <div class="toast-body">{1}</div></div>',c='<div class="progress">  <div class="progress-bar progress-bar-success progress-bar-striped progress-bar-animated" role="progressbar" style="width: {0}%">  </div></div>',n='<div class="alert alert-danger alert-dismissable" role="alert">\n  <span class="sr-only">Error:</span>\n  {0}\n  <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>\n</div>',p='<div class="alert alert-success alert-dismissable submit-row" role="alert">\n  <strong>Success!</strong>\n  {0}\n  <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>\n</div>',l='<button type="button" class="btn btn-primary" data-dismiss="modal">{0}</button>',u='<button type="button" class="btn btn-danger" data-dismiss="modal">No</button>',f='<button type="button" class="btn btn-primary" data-dismiss="modal">Yes</button>';function m(e){var t=a.format(e.title),o=(0,s.default)(t);"string"==typeof e.body?o.find(".modal-body").append("<p>".concat(e.body,"</p>")):o.find(".modal-body").append((0,s.default)(e.body));var i=(0,s.default)(l.format(e.button));return e.success&&(0,s.default)(i).click(function(){e.success()}),e.large&&o.find(".modal-dialog").addClass("modal-lg"),o.find(".modal-footer").append(i),o.find("pre code").each(function(e){r.default.highlightBlock(this)}),(0,s.default)("main").append(o),o.modal("show"),(0,s.default)(o).on("hidden.bs.modal",function(){(0,s.default)(this).modal("dispose")}),o}function h(e){(0,s.default)("#ezq--notifications-toast-container").length||(0,s.default)("body").append((0,s.default)("<div/>").attr({id:"ezq--notifications-toast-container"}).css({position:"fixed",bottom:"0",right:"0","min-width":"20%"}));var t,o=d.format(e.title,e.body),i=(0,s.default)(o);e.onclose&&(0,s.default)(i).find("button[data-dismiss=toast]").click(function(){e.onclose()}),e.onclick&&((t=(0,s.default)(i).find(".toast-body")).addClass("cursor-pointer"),t.click(function(){e.onclick()}));var n=!1!==e.autohide,r=!1!==e.animation,a=e.delay||1e4;return(0,s.default)("#ezq--notifications-toast-container").prepend(i),i.toast({autohide:n,delay:a,animation:r}),i.toast("show"),i}function g(e){var t=a.format(e.title),o=(0,s.default)(t);"string"==typeof e.body?o.find(".modal-body").append("<p>".concat(e.body,"</p>")):o.find(".modal-body").append((0,s.default)(e.body));var i=(0,s.default)(f),n=(0,s.default)(u);return o.find(".modal-footer").append(n),o.find(".modal-footer").append(i),o.find("pre code").each(function(e){r.default.highlightBlock(this)}),(0,s.default)("main").append(o),(0,s.default)(o).on("hidden.bs.modal",function(){(0,s.default)(this).modal("dispose")}),(0,s.default)(i).click(function(){e.success()}),o.modal("show"),o}function v(e){if(e.target){var t=(0,s.default)(e.target);return t.find(".progress-bar").css("width",e.width+"%"),t}var o=c.format(e.width),i=a.format(e.title),n=(0,s.default)(i);return n.find(".modal-body").append((0,s.default)(o)),(0,s.default)("main").append(n),n.modal("show")}function y(e){var t={success:p,error:n}[e.type].format(e.body);return(0,s.default)(t)}var j={ezAlert:m,ezToast:h,ezQuery:g,ezProgressBar:v,ezBadge:y};t.default=j},"./CTFd/themes/core/assets/js/fetch.js":function(e,t,o){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,o("./node_modules/whatwg-fetch/fetch.js");var i,n=(i=o("./CTFd/themes/core/assets/js/config.js"))&&i.__esModule?i:{default:i};var r=window.fetch;t.default=function(e,t){return void 0===t&&(t={method:"GET",credentials:"same-origin",headers:{}}),e=n.default.urlRoot+e,void 0===t.headers&&(t.headers={}),t.credentials="same-origin",t.headers.Accept="application/json",t.headers["Content-Type"]="application/json",t.headers["CSRF-Token"]=n.default.csrfNonce,r(e,t)}},"./CTFd/themes/core/assets/js/graphs.js":function(e,t,o){Object.defineProperty(t,"__esModule",{value:!0}),t.createGraph=function(e,t,o,i,n,r,a){var s=l[e],d=p.default.init(document.querySelector(t));d.setOption(s.format(i,n,r,a,o)),(0,c.default)(window).on("resize",function(){null!=d&&null!=d&&d.resize()})},t.updateGraph=function(e,t,o,i,n,r,a){var s=l[e];p.default.init(document.querySelector(t)).setOption(s.format(i,n,r,a,o))},t.disposeGraph=function(e){p.default.dispose(document.querySelector(e))};var c=i(o("./node_modules/jquery/dist/jquery.js")),p=i(o("./node_modules/echarts/dist/echarts-en.common.js")),f=i(o("./node_modules/dayjs/dayjs.min.js")),m=o("./CTFd/themes/core/assets/js/utils.js");function i(e){return e&&e.__esModule?e:{default:e}}var l={score_graph:{format:function(e,t,o,i,n){var r={title:{left:"center",text:"Score over Time"},tooltip:{trigger:"axis",axisPointer:{type:"cross"}},legend:{type:"scroll",orient:"horizontal",align:"left",bottom:0,data:[o]},toolbox:{feature:{saveAsImage:{}}},grid:{containLabel:!0},xAxis:[{type:"category",boundaryGap:!1,data:[]}],yAxis:[{type:"value"}],dataZoom:[{id:"dataZoomX",type:"slider",xAxisIndex:[0],filterMode:"filter",height:20,top:35,fillerColor:"rgba(233, 236, 241, 0.4)"}],series:[]},a=[],s=[],d=n[0].data,c=n[2].data,p=d.concat(c);p.sort(function(e,t){return new Date(e.date)-new Date(t.date)});for(var l=0;l<p.length;l++){var u=(0,f.default)(p[l].date);a.push(u.toDate());try{s.push(p[l].challenge.value)}catch(e){s.push(p[l].value)}}return a.forEach(function(e){r.xAxis[0].data.push(e)}),r.series.push({name:window.stats_data.name,type:"line",label:{normal:{show:!0,position:"top"}},areaStyle:{normal:{color:(0,m.colorHash)(o+t)}},itemStyle:{normal:{color:(0,m.colorHash)(o+t)}},data:(0,m.cumulativeSum)(s)}),r}},category_breakdown:{format:function(e,t,o,i,n){for(var r={title:{left:"center",text:"Category Breakdown"},tooltip:{trigger:"item"},toolbox:{show:!0,feature:{saveAsImage:{}}},legend:{type:"scroll",orient:"vertical",top:"middle",right:0,data:[]},series:[{name:"Category Breakdown",type:"pie",radius:["30%","50%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},itemStyle:{normal:{label:{show:!0,formatter:function(e){return"".concat(e.percent,"% (").concat(e.value,")")}},labelLine:{show:!0}},emphasis:{label:{show:!0,position:"center",textStyle:{fontSize:"14",fontWeight:"normal"}}}},emphasis:{label:{show:!0,fontSize:"30",fontWeight:"bold"}},labelLine:{show:!1},data:[]}]},a=n[0].data,s=[],d=0;d<a.length;d++)s.push(a[d].challenge.category);for(var c=s.filter(function(e,t){return s.indexOf(e)==t}),p=[],l=0;l<c.length;l++){for(var u=0,f=0;f<s.length;f++)s[f]==c[l]&&u++;p.push(u)}return c.forEach(function(e,t){r.legend.data.push(e),r.series[0].data.push({value:p[t],name:e,itemStyle:{color:(0,m.colorHash)(e)}})}),r}},solve_percentages:{format:function(e,t,o,i,n){var r=n[0].data.length;return{title:{left:"center",text:"Solve Percentages"},tooltip:{trigger:"item"},toolbox:{show:!0,feature:{saveAsImage:{}}},legend:{orient:"vertical",top:"middle",right:0,data:["Fails","Solves"]},series:[{name:"Solve Percentages",type:"pie",radius:["30%","50%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},itemStyle:{normal:{label:{show:!0,formatter:function(e){return"".concat(e.name," - ").concat(e.value," (").concat(e.percent,"%)")}},labelLine:{show:!0}},emphasis:{label:{show:!0,position:"center",textStyle:{fontSize:"14",fontWeight:"normal"}}}},emphasis:{label:{show:!0,fontSize:"30",fontWeight:"bold"}},labelLine:{show:!1},data:[{value:n[1].meta.count,name:"Fails",itemStyle:{color:"rgb(207, 38, 0)"}},{value:r,name:"Solves",itemStyle:{color:"rgb(0, 209, 64)"}}]}]}}}}},"./CTFd/themes/core/assets/js/pages/main.js":function(e,t,o){var i=m(o("./CTFd/themes/core/assets/js/CTFd.js")),n=m(o("./node_modules/jquery/dist/jquery.js")),r=m(o("./node_modules/dayjs/dayjs.min.js")),a=m(o("./node_modules/dayjs/plugin/advancedFormat.js")),s=m(o("./node_modules/nunjucks/browser/nunjucks.js")),d=o("./node_modules/howler/dist/howler.js"),c=m(o("./CTFd/themes/core/assets/js/events.js")),p=m(o("./CTFd/themes/core/assets/js/config.js")),l=m(o("./CTFd/themes/core/assets/js/styles.js")),u=m(o("./CTFd/themes/core/assets/js/times.js")),f=m(o("./CTFd/themes/core/assets/js/helpers.js"));function m(e){return e&&e.__esModule?e:{default:e}}r.default.extend(a.default),i.default.init(window.init),window.CTFd=i.default,window.helpers=f.default,window.$=n.default,window.dayjs=r.default,window.nunjucks=s.default,window.Howl=d.Howl,(0,n.default)(function(){(0,l.default)(),(0,u.default)(),(0,c.default)(p.default.urlRoot)})},"./CTFd/themes/core/assets/js/pages/stats.js":function(e,t,o){o("./CTFd/themes/core/assets/js/pages/main.js");var i=r(o("./node_modules/jquery/dist/jquery.js")),n=r(o("./CTFd/themes/core/assets/js/CTFd.js")),f=o("./CTFd/themes/core/assets/js/graphs.js");function r(e){return e&&e.__esModule?e:{default:e}}function m(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var o=[],i=!0,n=!1,r=void 0;try{for(var a,s=e[Symbol.iterator]();!(i=(a=s.next()).done)&&(o.push(a.value),!t||o.length!==t);i=!0);}catch(e){n=!0,r=e}finally{try{i||null==s.return||s.return()}finally{if(n)throw r}}return o}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return a(e,t);var o=Object.prototype.toString.call(e).slice(8,-1);"Object"===o&&e.constructor&&(o=e.constructor.name);if("Map"===o||"Set"===o)return Array.from(e);if("Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o))return a(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var o=0,i=new Array(t);o<t;o++)i[o]=e[o];return i}var h={team:[function(e){return n.default.api.get_team_solves({teamId:e})},function(e){return n.default.api.get_team_fails({teamId:e})},function(e){return n.default.api.get_team_awards({teamId:e})}],user:[function(e){return n.default.api.get_user_solves({userId:e})},function(e){return n.default.api.get_user_fails({userId:e})},function(e){return n.default.api.get_user_awards({userId:e})}]};(0,i.default)(function(){var t,o,i,n,e,r,a,s,d=window.stats_data,c=d.type,p=d.id,l=d.name,u=d.account_id;o=p,i=l,n=u,e=m(h[t=c],3),r=e[0],a=e[1],s=e[2],Promise.all([r(n),a(n),s(n)]).then(function(e){(0,f.createGraph)("score_graph","#score-graph",e,t,o,i,n),(0,f.createGraph)("category_breakdown","#categories-pie-graph",e,t,o,i,n),(0,f.createGraph)("solve_percentages","#keys-pie-graph",e,t,o,i,n)}),setInterval(function(){var t,o,i,n,e,r,a,s;o=p,i=l,n=u,e=m(h[t=c],3),r=e[0],a=e[1],s=e[2],Promise.all([r(n),a(n),s(n)]).then(function(e){(0,f.updateGraph)("score_graph","#score-graph",e,t,o,i,n),(0,f.updateGraph)("category_breakdown","#categories-pie-graph",e,t,o,i,n),(0,f.updateGraph)("solve_percentages","#keys-pie-graph",e,t,o,i,n)})},3e5)})},"./CTFd/themes/core/assets/js/patch.js":function(e,t,o){var i,s=(i=o("./node_modules/q/q.js"))&&i.__esModule?i:{default:i},n=o("./CTFd/themes/core/assets/js/api.js");function a(t,e){var o,i=Object.keys(t);return Object.getOwnPropertySymbols&&(o=Object.getOwnPropertySymbols(t),e&&(o=o.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,o)),i}function r(n){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?a(Object(r),!0).forEach(function(e){var t,o,i;t=n,i=r[o=e],o in t?Object.defineProperty(t,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(e){Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(r,e))})}return n}function d(e,t){return r(r({},e),t)}n.API.prototype.requestRaw=function(e,t,o,i,n,r,a,s){var d=r&&Object.keys(r).length?function(e){var t,o=[];for(t in e)e.hasOwnProperty(t)&&o.push(encodeURIComponent(t)+"="+encodeURIComponent(e[t]));return o.join("&")}(r):null,c=t+(d?"?"+d:"");i&&!Object.keys(i).length&&(i=void 0),fetch(c,{method:e,headers:n,body:i}).then(function(e){return e.json()}).then(function(e){s.resolve(e)}).catch(function(e){s.reject(e)})},n.API.prototype.patch_user_public=function(e,t){void 0===e&&(e={});var o,i=s.default.defer(),n=this.domain,r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],o="/users/{user_id}".replace("{user_id}",e.userId),void 0===e.userId?i.reject(new Error("Missing required  parameter: userId")):this.request("PATCH",n+o,e,t,r,{},{},i),i.promise},n.API.prototype.patch_user_private=function(e,t){void 0===e&&(e={});var o=s.default.defer(),i=this.domain,n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],this.request("PATCH",i+"/users/me",e,t,n,{},{},o),o.promise},n.API.prototype.post_unlock_list=function(e,t){var o=s.default.defer(),i=this.domain,n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],this.request("POST",i+"/unlocks",e,t,n,{},{},o),o.promise},n.API.prototype.post_notification_list=function(e,t){void 0===e&&(e={});var o=s.default.defer(),i=this.domain,n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],this.request("POST",i+"/notifications",e,t,n,{},{},o),o.promise},n.API.prototype.post_files_list=function(e,t){var o=s.default.defer(),i=this.domain,n={};return n.Accept=["application/json"],n["Content-Type"]=["application/json"],this.requestRaw("POST",i+"/files",e,t,n,{},{},o),o.promise},n.API.prototype.patch_config=function(e,t){void 0===e&&(e={});var o,i=s.default.defer(),n=this.domain,r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],o="/configs/{config_key}".replace("{config_key}",e.configKey),void 0===e.configKey?i.reject(new Error("Missing required  parameter: configKey")):this.request("PATCH",n+o,e,t,r,{},{},i),i.promise},n.API.prototype.patch_config_list=function(e,t){void 0===e&&(e={});var o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],n=d(e,n),this.request("PATCH",i+"/configs",e,t,r,n,{},o),o.promise},n.API.prototype.post_tag_list=function(e,t){void 0===e&&(e={});var o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],n=d(e,n),this.request("POST",i+"/tags",e,t,r,n,{},o),o.promise},n.API.prototype.patch_team_public=function(e,t){void 0===e&&(e={});var o,i=s.default.defer(),n=this.domain,r={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],o="/teams/{team_id}".replace("{team_id}",e.teamId),void 0===e.teamId?i.reject(new Error("Missing required  parameter: teamId")):(r=d(e,r),this.request("PATCH",n+o,e,t,a,r,{},i)),i.promise},n.API.prototype.post_challenge_attempt=function(e,t){void 0===e&&(e={});var o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],n=d(e,n),this.request("POST",i+"/challenges/attempt",e,t,r,n,{},o),o.promise},n.API.prototype.get_hint=function(e){void 0===e&&(e={});var t,o=s.default.defer(),i=this.domain,n={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],t="/hints/{hint_id}".replace("{hint_id}",e.hintId),void 0===e.hintId?o.reject(new Error("Missing required  parameter: hintId")):(delete e.hintId,n=d(e,n),this.request("GET",i+t,e,{},r,n,{},o)),o.promise}},"./CTFd/themes/core/assets/js/styles.js":function(e,t,o){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,o("./node_modules/bootstrap/dist/js/bootstrap.bundle.js");var i=r(o("./node_modules/jquery/dist/jquery.js")),n=r(o("./node_modules/highlight.js/lib/index.js"));function r(e){return e&&e.__esModule?e:{default:e}}t.default=function(){(0,i.default)(":input").each(function(){(0,i.default)(this).data("initial",(0,i.default)(this).val())}),(0,i.default)(".form-control").bind({focus:function(){(0,i.default)(this).removeClass("input-filled-invalid"),(0,i.default)(this).addClass("input-filled-valid")},blur:function(){""===(0,i.default)(this).val()&&((0,i.default)(this).removeClass("input-filled-invalid"),(0,i.default)(this).removeClass("input-filled-valid"))}}),(0,i.default)(".form-control").each(function(){(0,i.default)(this).val()&&(0,i.default)(this).addClass("input-filled-valid")}),(0,i.default)(".page-select").change(function(){var e=new URL(window.location);e.searchParams.set("page",this.value),window.location.href=e.toString()}),(0,i.default)('[data-toggle="tooltip"]').tooltip(),(0,i.default)(function(){document.querySelectorAll("pre code").forEach(function(e){n.default.highlightBlock(e)})})}},"./CTFd/themes/core/assets/js/times.js":function(e,t,o){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(o("./node_modules/dayjs/dayjs.min.js")),i=n(o("./node_modules/dayjs/plugin/advancedFormat.js")),a=n(o("./node_modules/jquery/dist/jquery.js"));function n(e){return e&&e.__esModule?e:{default:e}}r.default.extend(i.default);t.default=function(){(0,a.default)("[data-time]").each(function(e,t){var o=(0,a.default)(t),i=o.data("time"),n=o.data("time-format")||"MMMM Do, h:mm:ss A";t.innerText=(0,r.default)(i).format(n)})}},"./CTFd/themes/core/assets/js/utils.js":function(e,t,o){Object.defineProperty(t,"__esModule",{value:!0}),t.WindowController=n,t.colorHash=function(e){for(var t=0,o=0;o<e.length;o++)t=e.charCodeAt(o)+((t<<5)-t),t&=t;var i=(t%25+25)%25+75,n=(t%20+20)%20+40;return"hsl(".concat((t%360+360)%360,", ").concat(i,"%, ").concat(n,"%)")},t.cumulativeSum=function(e){for(var t=e.concat(),o=0;o<e.length;o++)t[o]=e.slice(0,o+1).reduce(function(e,t){return e+t});return t},t.init_notification_counter=function(){var e=r.getItem(s);null===e?r.setItem(s,0):0<e&&(0,a.default)(".badge-notification").text(e)},t.set_notification_counter=function(e){r.setItem(s,e)},t.inc_notification_counter=function(){var e=r.getItem(s)||0;r.setItem(s,++e),(0,a.default)(".badge-notification").text(e)},t.dec_notification_counter=function(){var e=r.getItem(s)||0;0<e&&(r.setItem(s,--e),(0,a.default)(".badge-notification").text(e));0==e&&d()},t.clear_notification_counter=d,t.copyToClipboard=function(e,t){(0,a.default)(t).select(),document.execCommand("copy"),(0,a.default)(e.target).tooltip({title:"Copied!",trigger:"manual"}),(0,a.default)(e.target).tooltip("show"),setTimeout(function(){(0,a.default)(e.target).tooltip("hide")},1500)},t.makeSortableTables=function(){function r(e,t){return(0,a.default)(e).children("td").eq(t).text()}(0,a.default)("th.sort-col").append(' <i class="fas fa-sort"></i>'),(0,a.default)("th.sort-col").click(function(){var n,e=(0,a.default)(this).parents("table").eq(0),t=e.find("tr:gt(0)").toArray().sort((n=(0,a.default)(this).index(),function(e,t){var o=r(e,n),i=r(t,n);return a.default.isNumeric(o)&&a.default.isNumeric(i)?o-i:o.toString().localeCompare(i)}));this.asc=!this.asc,this.asc||(t=t.reverse());for(var o=0;o<t.length;o++)e.append(t[o])})},t.getScript=function(i){return new Promise(function(e,t){var o=document.createElement("script");document.body.appendChild(o),o.onload=e,o.onerror=t,o.async=!0,o.src=i})},t.createHtmlNode=function(e){var t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild},t.htmlEntities=function(e){return(0,a.default)("<div/>").text(e).html()};var i,a=(i=o("./node_modules/jquery/dist/jquery.js"))&&i.__esModule?i:{default:i};function n(){this.id=Math.random(),this.isMaster=!1,this.others={},window.addEventListener("storage",this,!1),window.addEventListener("unload",this,!1),this.broadcast("hello");var t=this;this._checkTimeout=setTimeout(function e(){t.check(),t._checkTimeout=setTimeout(e,9e3)},500),this._pingTimeout=setTimeout(function e(){t.sendPing(),t._pingTimeout=setTimeout(e,17e3)},17e3)}a.default.fn.serializeJSON=function(o){var i={},n=(0,a.default)(this),e=n.serializeArray();return(e=(e=e.concat(n.find("input[type=checkbox]:checked").map(function(){return{name:this.name,value:!0}}).get())).concat(n.find("input[type=checkbox]:not(:checked)").map(function(){return{name:this.name,value:!1}}).get())).map(function(e){var t;o&&(null===e.value||""===e.value)&&(t=n.find(":input[name='".concat(e.name,"']"))).data("initial")===t.val()||(i[e.name]=e.value)}),i},String.prototype.format=String.prototype.f=function(){for(var e=this,t=arguments.length;t--;)e=e.replace(new RegExp("\\{"+t+"\\}","gm"),arguments[t]);return e},String.prototype.hashCode=function(){var e,t,o=0;if(0==this.length)return o;for(e=0,t=this.length;e<t;e++)o=(o<<5)-o+this.charCodeAt(e),o|=0;return o},n.prototype.destroy=function(){clearTimeout(this._pingTimeout),clearTimeout(this._checkTimeout),window.removeEventListener("storage",this,!1),window.removeEventListener("unload",this,!1),this.broadcast("bye")},n.prototype.handleEvent=function(e){if("unload"===e.type)this.destroy();else if("broadcast"===e.key)try{var t=JSON.parse(e.newValue);t.id!==this.id&&this[t.type](t)}catch(e){}},n.prototype.sendPing=function(){this.broadcast("ping")},n.prototype.hello=function(e){this.ping(e),e.id<this.id?this.check():this.sendPing()},n.prototype.ping=function(e){this.others[e.id]=+new Date},n.prototype.bye=function(e){delete this.others[e.id],this.check()},n.prototype.check=function(e){var t,o=+new Date,i=!0;for(t in this.others)this.others[t]+23e3<o?delete this.others[t]:t<this.id&&(i=!1);this.isMaster!==i&&(this.isMaster=i,this.masterDidChange())},n.prototype.masterDidChange=function(){},n.prototype.broadcast=function(e,t){var o,i={id:this.id,type:e};for(o in t)i[o]=t[o];try{localStorage.setItem("broadcast",JSON.stringify(i))}catch(e){}};var r=window.localStorage,s="unread_notifications";function d(){r.setItem(s,0),(0,a.default)(".badge-notification").empty()}},0:function(e,t){},1:function(e,t){}});
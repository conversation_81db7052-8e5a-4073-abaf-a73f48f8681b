(window["webpackJsonp"] = window["webpackJsonp"] || []).push([["default~pages/challenges~pages/main~pages/notifications~pages/scoreboard~pages/settings~pages/setup~~6822bf1f"],{

/***/ "./CTFd/themes/core/assets/js/CTFd.js":
/*!********************************************!*\
  !*** ./CTFd/themes/core/assets/js/CTFd.js ***!
  \********************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

;
eval("\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _jquery = _interopRequireDefault(__webpack_require__(/*! jquery */ \"./node_modules/jquery/dist/jquery.js\"));\n\nvar _dayjs = _interopRequireDefault(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\"));\n\nvar _markdownIt = _interopRequireDefault(__webpack_require__(/*! markdown-it */ \"./node_modules/markdown-it/index.js\"));\n\n__webpack_require__(/*! ./patch */ \"./CTFd/themes/core/assets/js/patch.js\");\n\nvar _fetch = _interopRequireDefault(__webpack_require__(/*! ./fetch */ \"./CTFd/themes/core/assets/js/fetch.js\"));\n\nvar _config = _interopRequireDefault(__webpack_require__(/*! ./config */ \"./CTFd/themes/core/assets/js/config.js\"));\n\nvar _api = __webpack_require__(/*! ./api */ \"./CTFd/themes/core/assets/js/api.js\");\n\nvar _ezq = _interopRequireDefault(__webpack_require__(/*! ./ezq */ \"./CTFd/themes/core/assets/js/ezq.js\"));\n\nvar _utils = __webpack_require__(/*! ./utils */ \"./CTFd/themes/core/assets/js/utils.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nvar api = new _api.API(\"/\");\nvar user = {};\nvar _internal = {};\nvar ui = {\n  ezq: _ezq[\"default\"]\n};\nvar lib = {\n  $: _jquery[\"default\"],\n  markdown: markdown,\n  dayjs: _dayjs[\"default\"]\n};\nvar initialized = false;\n\nvar init = function init(data) {\n  if (initialized) {\n    return;\n  }\n\n  initialized = true;\n  _config[\"default\"].urlRoot = data.urlRoot || _config[\"default\"].urlRoot;\n  _config[\"default\"].csrfNonce = data.csrfNonce || _config[\"default\"].csrfNonce;\n  _config[\"default\"].userMode = data.userMode || _config[\"default\"].userMode;\n  api.domain = _config[\"default\"].urlRoot + \"/api/v1\";\n  user.id = data.userId;\n};\n\nvar plugin = {\n  run: function run(f) {\n    f(CTFd);\n  }\n};\n\nfunction markdown(config) {\n  // Merge passed config with original. Default to original.\n  var md_config = _objectSpread(_objectSpread({}, {\n    html: true,\n    linkify: true\n  }), config);\n\n  var md = (0, _markdownIt[\"default\"])(md_config);\n\n  md.renderer.rules.link_open = function (tokens, idx, options, env, self) {\n    tokens[idx].attrPush([\"target\", \"_blank\"]);\n    return self.renderToken(tokens, idx, options);\n  };\n\n  return md;\n}\n\nvar utils = {\n  ajax: {\n    getScript: _utils.getScript\n  },\n  html: {\n    createHtmlNode: _utils.createHtmlNode,\n    htmlEntities: _utils.htmlEntities\n  }\n};\nvar CTFd = {\n  init: init,\n  config: _config[\"default\"],\n  fetch: _fetch[\"default\"],\n  user: user,\n  ui: ui,\n  utils: utils,\n  api: api,\n  lib: lib,\n  _internal: _internal,\n  plugin: plugin\n};\nvar _default = CTFd;\nexports[\"default\"] = _default;\n\n//# sourceURL=webpack:///./CTFd/themes/core/assets/js/CTFd.js?");

/***/ }),

/***/ "./CTFd/themes/core/assets/js/api.js":
/*!*******************************************!*\
  !*** ./CTFd/themes/core/assets/js/api.js ***!
  \*******************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

;
eval("\n\nvar _fetch = _interopRequireDefault(__webpack_require__(/*! ./fetch */ \"./CTFd/themes/core/assets/js/fetch.js\"));\n\nvar _q = _interopRequireDefault(__webpack_require__(/*! q */ \"./node_modules/q/q.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\n/**\n *\n * @class API\n * @param {(string|object)} [domainOrOptions] - The project domain or options object. If object, see the object's optional properties.\n * @param {string} [domainOrOptions.domain] - The project domain\n * @param {object} [domainOrOptions.token] - auth token - object with value property and optional headerOrQueryName and isQuery properties\n */\nvar API = function () {\n  \"use strict\";\n\n  function API(options) {\n    var domain = _typeof(options) === \"object\" ? options.domain : options;\n    this.domain = domain ? domain : \"\";\n\n    if (this.domain.length === 0) {\n      throw new Error(\"Domain parameter must be specified as a string.\");\n    }\n  }\n\n  function serializeQueryParams(parameters) {\n    var str = [];\n\n    for (var p in parameters) {\n      if (parameters.hasOwnProperty(p)) {\n        str.push(encodeURIComponent(p) + \"=\" + encodeURIComponent(parameters[p]));\n      }\n    }\n\n    return str.join(\"&\");\n  }\n\n  function mergeQueryParams(parameters, queryParameters) {\n    if (parameters.$queryParameters) {\n      Object.keys(parameters.$queryParameters).forEach(function (parameterName) {\n        var parameter = parameters.$queryParameters[parameterName];\n        queryParameters[parameterName] = parameter;\n      });\n    }\n\n    return queryParameters;\n  }\n  /**\n   * HTTP Request\n   * @method\n   * @name API#request\n   * @param {string} method - http method\n   * @param {string} url - url to do request\n   * @param {object} parameters\n   * @param {object} body - body parameters / object\n   * @param {object} headers - header parameters\n   * @param {object} queryParameters - querystring parameters\n   * @param {object} form - form data object\n   * @param {object} deferred - promise object\n   */\n\n\n  API.prototype.request = function (method, url, parameters, body, headers, queryParameters, form, deferred) {\n    var queryParams = queryParameters && Object.keys(queryParameters).length ? serializeQueryParams(queryParameters) : null;\n    var urlWithParams = url + (queryParams ? \"?\" + queryParams : \"\");\n\n    if (body && !Object.keys(body).length) {\n      body = undefined;\n    }\n\n    (0, _fetch[\"default\"])(urlWithParams, {\n      method: method,\n      headers: headers,\n      body: JSON.stringify(body)\n    }).then(function (response) {\n      return response.json();\n    }).then(function (body) {\n      deferred.resolve(body);\n    })[\"catch\"](function (error) {\n      deferred.reject(error);\n    });\n  };\n  /**\n   *\n   * @method\n   * @name API#post_award_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.post_award_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/awards\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"POST\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#delete_award\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.awardId - An Award ID\n   */\n\n\n  API.prototype.delete_award = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/awards/{award_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{award_id}\", parameters[\"awardId\"]);\n\n    if (parameters[\"awardId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: awardId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"DELETE\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_award\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.awardId - An Award ID\n   */\n\n\n  API.prototype.get_award = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/awards/{award_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{award_id}\", parameters[\"awardId\"]);\n\n    if (parameters[\"awardId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: awardId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#post_challenge_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.post_challenge_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/challenges\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"POST\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_challenge_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.get_challenge_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/challenges\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#post_challenge_attempt\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.post_challenge_attempt = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/challenges/attempt\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"POST\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_challenge_types\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.get_challenge_types = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/challenges/types\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#patch_challenge\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.challengeId - A Challenge ID\n   */\n\n\n  API.prototype.patch_challenge = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/challenges/{challenge_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{challenge_id}\", parameters[\"challengeId\"]);\n\n    if (parameters[\"challengeId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: challengeId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"PATCH\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#delete_challenge\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.challengeId - A Challenge ID\n   */\n\n\n  API.prototype.delete_challenge = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/challenges/{challenge_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{challenge_id}\", parameters[\"challengeId\"]);\n\n    if (parameters[\"challengeId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: challengeId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"DELETE\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_challenge\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.challengeId - A Challenge ID\n   */\n\n\n  API.prototype.get_challenge = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/challenges/{challenge_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{challenge_id}\", parameters[\"challengeId\"]);\n\n    if (parameters[\"challengeId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: challengeId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_challenge_files\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.id - A Challenge ID\n   * @param {string} parameters.challengeId -\n   */\n\n\n  API.prototype.get_challenge_files = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/challenges/{challenge_id}/files\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n\n    if (parameters[\"id\"] !== undefined) {\n      queryParameters[\"id\"] = parameters[\"id\"];\n    }\n\n    path = path.replace(\"{challenge_id}\", parameters[\"challengeId\"]);\n\n    if (parameters[\"challengeId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: challengeId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_challenge_flags\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.id - A Challenge ID\n   * @param {string} parameters.challengeId -\n   */\n\n\n  API.prototype.get_challenge_flags = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/challenges/{challenge_id}/flags\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n\n    if (parameters[\"id\"] !== undefined) {\n      queryParameters[\"id\"] = parameters[\"id\"];\n    }\n\n    path = path.replace(\"{challenge_id}\", parameters[\"challengeId\"]);\n\n    if (parameters[\"challengeId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: challengeId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_challenge_hints\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.id - A Challenge ID\n   * @param {string} parameters.challengeId -\n   */\n\n\n  API.prototype.get_challenge_hints = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/challenges/{challenge_id}/hints\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n\n    if (parameters[\"id\"] !== undefined) {\n      queryParameters[\"id\"] = parameters[\"id\"];\n    }\n\n    path = path.replace(\"{challenge_id}\", parameters[\"challengeId\"]);\n\n    if (parameters[\"challengeId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: challengeId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_challenge_solves\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.id - A Challenge ID\n   * @param {string} parameters.challengeId -\n   */\n\n\n  API.prototype.get_challenge_solves = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/challenges/{challenge_id}/solves\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n\n    if (parameters[\"id\"] !== undefined) {\n      queryParameters[\"id\"] = parameters[\"id\"];\n    }\n\n    path = path.replace(\"{challenge_id}\", parameters[\"challengeId\"]);\n\n    if (parameters[\"challengeId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: challengeId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_challenge_tags\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.id - A Challenge ID\n   * @param {string} parameters.challengeId -\n   */\n\n\n  API.prototype.get_challenge_tags = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/challenges/{challenge_id}/tags\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n\n    if (parameters[\"id\"] !== undefined) {\n      queryParameters[\"id\"] = parameters[\"id\"];\n    }\n\n    path = path.replace(\"{challenge_id}\", parameters[\"challengeId\"]);\n\n    if (parameters[\"challengeId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: challengeId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#post_config_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.post_config_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/configs\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"POST\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#patch_config_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.patch_config_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/configs\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"PATCH\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_config_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.get_config_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/configs\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#patch_config\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.configKey -\n   */\n\n\n  API.prototype.patch_config = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/configs/{config_key}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{config_key}\", parameters[\"configKey\"]);\n\n    if (parameters[\"configKey\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: configKey\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"PATCH\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#delete_config\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.configKey -\n   */\n\n\n  API.prototype.delete_config = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/configs/{config_key}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{config_key}\", parameters[\"configKey\"]);\n\n    if (parameters[\"configKey\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: configKey\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"DELETE\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_config\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.configKey -\n   */\n\n\n  API.prototype.get_config = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/configs/{config_key}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{config_key}\", parameters[\"configKey\"]);\n\n    if (parameters[\"configKey\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: configKey\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#post_files_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.post_files_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/files\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"POST\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_files_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.get_files_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/files\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#delete_files_detail\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.fileId -\n   */\n\n\n  API.prototype.delete_files_detail = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/files/{file_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{file_id}\", parameters[\"fileId\"]);\n\n    if (parameters[\"fileId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: fileId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"DELETE\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_files_detail\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.fileId -\n   */\n\n\n  API.prototype.get_files_detail = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/files/{file_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{file_id}\", parameters[\"fileId\"]);\n\n    if (parameters[\"fileId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: fileId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#post_flag_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.post_flag_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/flags\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"POST\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_flag_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.get_flag_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/flags\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_flag_types\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.get_flag_types = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/flags/types\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_flag_types_1\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.typeName -\n   */\n\n\n  API.prototype.get_flag_types_1 = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/flags/types/{type_name}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{type_name}\", parameters[\"typeName\"]);\n\n    if (parameters[\"typeName\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: typeName\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#patch_flag\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.flagId -\n   */\n\n\n  API.prototype.patch_flag = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/flags/{flag_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{flag_id}\", parameters[\"flagId\"]);\n\n    if (parameters[\"flagId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: flagId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"PATCH\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#delete_flag\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.flagId -\n   */\n\n\n  API.prototype.delete_flag = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/flags/{flag_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{flag_id}\", parameters[\"flagId\"]);\n\n    if (parameters[\"flagId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: flagId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"DELETE\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_flag\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.flagId -\n   */\n\n\n  API.prototype.get_flag = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/flags/{flag_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{flag_id}\", parameters[\"flagId\"]);\n\n    if (parameters[\"flagId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: flagId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#post_hint_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.post_hint_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/hints\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"POST\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_hint_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.get_hint_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/hints\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#patch_hint\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.hintId -\n   */\n\n\n  API.prototype.patch_hint = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/hints/{hint_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{hint_id}\", parameters[\"hintId\"]);\n\n    if (parameters[\"hintId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: hintId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"PATCH\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#delete_hint\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.hintId -\n   */\n\n\n  API.prototype.delete_hint = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/hints/{hint_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{hint_id}\", parameters[\"hintId\"]);\n\n    if (parameters[\"hintId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: hintId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"DELETE\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_hint\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.hintId -\n   */\n\n\n  API.prototype.get_hint = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/hints/{hint_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{hint_id}\", parameters[\"hintId\"]);\n\n    if (parameters[\"hintId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: hintId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#post_notification_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.post_notification_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/notifications\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"POST\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_notification_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.get_notification_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/notifications\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#delete_notification\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.notificationId - A Notification ID\n   */\n\n\n  API.prototype.delete_notification = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/notifications/{notification_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{notification_id}\", parameters[\"notificationId\"]);\n\n    if (parameters[\"notificationId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: notificationId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"DELETE\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_notification\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.notificationId - A Notification ID\n   */\n\n\n  API.prototype.get_notification = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/notifications/{notification_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{notification_id}\", parameters[\"notificationId\"]);\n\n    if (parameters[\"notificationId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: notificationId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#post_page_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.post_page_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/pages\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"POST\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_page_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.get_page_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/pages\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#patch_page_detail\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.pageId -\n   */\n\n\n  API.prototype.patch_page_detail = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/pages/{page_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{page_id}\", parameters[\"pageId\"]);\n\n    if (parameters[\"pageId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: pageId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"PATCH\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#delete_page_detail\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.pageId -\n   */\n\n\n  API.prototype.delete_page_detail = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/pages/{page_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{page_id}\", parameters[\"pageId\"]);\n\n    if (parameters[\"pageId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: pageId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"DELETE\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_page_detail\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.pageId -\n   */\n\n\n  API.prototype.get_page_detail = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/pages/{page_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{page_id}\", parameters[\"pageId\"]);\n\n    if (parameters[\"pageId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: pageId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_scoreboard_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.get_scoreboard_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/scoreboard\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_scoreboard_detail\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.count - How many top teams to return\n   */\n\n\n  API.prototype.get_scoreboard_detail = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/scoreboard/top/{count}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{count}\", parameters[\"count\"]);\n\n    if (parameters[\"count\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: count\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_challenge_solve_statistics\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.get_challenge_solve_statistics = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/statistics/challenges/solves\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_challenge_solve_percentages\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.get_challenge_solve_percentages = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/statistics/challenges/solves/percentages\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_challenge_property_counts\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.column -\n   */\n\n\n  API.prototype.get_challenge_property_counts = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/statistics/challenges/{column}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{column}\", parameters[\"column\"]);\n\n    if (parameters[\"column\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: column\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_submission_property_counts\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.column -\n   */\n\n\n  API.prototype.get_submission_property_counts = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/statistics/submissions/{column}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{column}\", parameters[\"column\"]);\n\n    if (parameters[\"column\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: column\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_team_statistics\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.get_team_statistics = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/statistics/teams\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_user_statistics\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.get_user_statistics = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/statistics/users\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_user_property_counts\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.column -\n   */\n\n\n  API.prototype.get_user_property_counts = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/statistics/users/{column}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{column}\", parameters[\"column\"]);\n\n    if (parameters[\"column\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: column\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#post_submissions_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.post_submissions_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/submissions\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"POST\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_submissions_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.get_submissions_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/submissions\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#delete_submission\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.submissionId - A Submission ID\n   */\n\n\n  API.prototype.delete_submission = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/submissions/{submission_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{submission_id}\", parameters[\"submissionId\"]);\n\n    if (parameters[\"submissionId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: submissionId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"DELETE\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_submission\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.submissionId - A Submission ID\n   */\n\n\n  API.prototype.get_submission = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/submissions/{submission_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{submission_id}\", parameters[\"submissionId\"]);\n\n    if (parameters[\"submissionId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: submissionId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#post_tag_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.post_tag_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/tags\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"POST\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_tag_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.get_tag_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/tags\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#patch_tag\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.tagId - A Tag ID\n   */\n\n\n  API.prototype.patch_tag = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/tags/{tag_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{tag_id}\", parameters[\"tagId\"]);\n\n    if (parameters[\"tagId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: tagId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"PATCH\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#delete_tag\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.tagId - A Tag ID\n   */\n\n\n  API.prototype.delete_tag = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/tags/{tag_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{tag_id}\", parameters[\"tagId\"]);\n\n    if (parameters[\"tagId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: tagId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"DELETE\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_tag\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.tagId - A Tag ID\n   */\n\n\n  API.prototype.get_tag = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/tags/{tag_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{tag_id}\", parameters[\"tagId\"]);\n\n    if (parameters[\"tagId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: tagId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#post_team_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.post_team_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/teams\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"POST\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_team_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.get_team_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/teams\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#patch_team_private\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.teamId - Current Team\n   */\n\n\n  API.prototype.patch_team_private = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/teams/me\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n\n    if (parameters[\"teamId\"] !== undefined) {\n      queryParameters[\"team_id\"] = parameters[\"teamId\"];\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"PATCH\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_team_private\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.teamId - Current Team\n   */\n\n\n  API.prototype.get_team_private = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/teams/me\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n\n    if (parameters[\"teamId\"] !== undefined) {\n      queryParameters[\"team_id\"] = parameters[\"teamId\"];\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#patch_team_public\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.teamId - Team ID\n   */\n\n\n  API.prototype.patch_team_public = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/teams/{team_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{team_id}\", parameters[\"teamId\"]);\n\n    if (parameters[\"teamId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: teamId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"PATCH\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#delete_team_public\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.teamId - Team ID\n   */\n\n\n  API.prototype.delete_team_public = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/teams/{team_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{team_id}\", parameters[\"teamId\"]);\n\n    if (parameters[\"teamId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: teamId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"DELETE\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_team_public\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.teamId - Team ID\n   */\n\n\n  API.prototype.get_team_public = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/teams/{team_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{team_id}\", parameters[\"teamId\"]);\n\n    if (parameters[\"teamId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: teamId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_team_awards\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.teamId - Team ID or 'me'\n   */\n\n\n  API.prototype.get_team_awards = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/teams/{team_id}/awards\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{team_id}\", parameters[\"teamId\"]);\n\n    if (parameters[\"teamId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: teamId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_team_fails\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.teamId - Team ID or 'me'\n   */\n\n\n  API.prototype.get_team_fails = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/teams/{team_id}/fails\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{team_id}\", parameters[\"teamId\"]);\n\n    if (parameters[\"teamId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: teamId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_team_solves\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.teamId - Team ID or 'me'\n   */\n\n\n  API.prototype.get_team_solves = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/teams/{team_id}/solves\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{team_id}\", parameters[\"teamId\"]);\n\n    if (parameters[\"teamId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: teamId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#post_unlock_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.post_unlock_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/unlocks\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"POST\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_unlock_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.get_unlock_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/unlocks\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#post_user_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.post_user_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/users\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"POST\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_user_list\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.get_user_list = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/users\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#patch_user_private\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.patch_user_private = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/users/me\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"PATCH\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_user_private\n   * @param {object} parameters - method options and parameters\n   */\n\n\n  API.prototype.get_user_private = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/users/me\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#patch_user_public\n   * @param {object} parameters - method options and parameters\n   * @param {integer} parameters.userId - User ID\n   */\n\n\n  API.prototype.patch_user_public = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/users/{user_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{user_id}\", parameters[\"userId\"]);\n\n    if (parameters[\"userId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: userId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"PATCH\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#delete_user_public\n   * @param {object} parameters - method options and parameters\n   * @param {integer} parameters.userId - User ID\n   */\n\n\n  API.prototype.delete_user_public = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/users/{user_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{user_id}\", parameters[\"userId\"]);\n\n    if (parameters[\"userId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: userId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"DELETE\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_user_public\n   * @param {object} parameters - method options and parameters\n   * @param {integer} parameters.userId - User ID\n   */\n\n\n  API.prototype.get_user_public = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/users/{user_id}\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{user_id}\", parameters[\"userId\"]);\n\n    if (parameters[\"userId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: userId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_user_awards\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.userId - User ID or 'me'\n   */\n\n\n  API.prototype.get_user_awards = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/users/{user_id}/awards\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{user_id}\", parameters[\"userId\"]);\n\n    if (parameters[\"userId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: userId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_user_fails\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.userId - User ID or 'me'\n   */\n\n\n  API.prototype.get_user_fails = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/users/{user_id}/fails\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{user_id}\", parameters[\"userId\"]);\n\n    if (parameters[\"userId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: userId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n  /**\n   *\n   * @method\n   * @name API#get_user_solves\n   * @param {object} parameters - method options and parameters\n   * @param {string} parameters.userId - User ID or 'me'\n   */\n\n\n  API.prototype.get_user_solves = function (parameters) {\n    if (parameters === undefined) {\n      parameters = {};\n    }\n\n    var deferred = _q[\"default\"].defer();\n\n    var domain = this.domain,\n        path = \"/users/{user_id}/solves\";\n    var body = {},\n        queryParameters = {},\n        headers = {},\n        form = {};\n    headers[\"Accept\"] = [\"application/json\"];\n    headers[\"Content-Type\"] = [\"application/json\"];\n    path = path.replace(\"{user_id}\", parameters[\"userId\"]);\n\n    if (parameters[\"userId\"] === undefined) {\n      deferred.reject(new Error(\"Missing required  parameter: userId\"));\n      return deferred.promise;\n    }\n\n    queryParameters = mergeQueryParams(parameters, queryParameters);\n    this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n    return deferred.promise;\n  };\n\n  return API;\n}(); // eslint-disable-next-line no-undef\n\n\nexports.API = API;\n\n//# sourceURL=webpack:///./CTFd/themes/core/assets/js/api.js?");

/***/ }),

/***/ "./CTFd/themes/core/assets/js/config.js":
/*!**********************************************!*\
  !*** ./CTFd/themes/core/assets/js/config.js ***!
  \**********************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

;
eval("\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _default = {\n  urlRoot: \"\",\n  csrfNonce: \"\",\n  userMode: \"\"\n};\nexports[\"default\"] = _default;\n\n//# sourceURL=webpack:///./CTFd/themes/core/assets/js/config.js?");

/***/ }),

/***/ "./CTFd/themes/core/assets/js/events.js":
/*!**********************************************!*\
  !*** ./CTFd/themes/core/assets/js/events.js ***!
  \**********************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

;
eval("\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _howler = __webpack_require__(/*! howler */ \"./node_modules/howler/dist/howler.js\");\n\nvar _eventSourcePolyfill = __webpack_require__(/*! event-source-polyfill */ \"./node_modules/event-source-polyfill/src/eventsource.js\");\n\nvar _ezq = __webpack_require__(/*! ./ezq */ \"./CTFd/themes/core/assets/js/ezq.js\");\n\nvar _utils = __webpack_require__(/*! ./utils */ \"./CTFd/themes/core/assets/js/utils.js\");\n\nvar EventSource = _eventSourcePolyfill.NativeEventSource || _eventSourcePolyfill.EventSourcePolyfill;\n\nvar _default = function _default(root) {\n  var source = new EventSource(root + \"/events\");\n  var wc = new _utils.WindowController();\n  var howl = new _howler.Howl({\n    src: [root + \"/themes/core/static/sounds/notification.webm\", root + \"/themes/core/static/sounds/notification.mp3\"]\n  });\n  (0, _utils.init_notification_counter)();\n\n  function connect() {\n    source.addEventListener(\"notification\", function (event) {\n      var data = JSON.parse(event.data);\n      wc.broadcast(\"notification\", data); // Render in the master tab\n\n      render(data); // Only play sounds in the master tab\n\n      if (data.sound) {\n        howl.play();\n      }\n    }, false);\n  }\n\n  function disconnect() {\n    if (source) {\n      source.close();\n    }\n  }\n\n  function render(data) {\n    switch (data.type) {\n      case \"toast\":\n        {\n          (0, _utils.inc_notification_counter)(); // Trim toast body to length\n\n          var length = 50;\n          var trimmed_content = data.content.length > length ? data.content.substring(0, length - 3) + \"...\" : data.content;\n          var clicked = false;\n          (0, _ezq.ezToast)({\n            title: data.title,\n            body: trimmed_content,\n            onclick: function onclick() {\n              (0, _ezq.ezAlert)({\n                title: data.title,\n                body: data.html,\n                button: \"Got it!\",\n                success: function success() {\n                  clicked = true;\n                  (0, _utils.dec_notification_counter)();\n                }\n              });\n            },\n            onclose: function onclose() {\n              if (!clicked) {\n                (0, _utils.dec_notification_counter)();\n              }\n            }\n          });\n          break;\n        }\n\n      case \"alert\":\n        {\n          (0, _utils.inc_notification_counter)();\n          (0, _ezq.ezAlert)({\n            title: data.title,\n            body: data.html,\n            button: \"Got it!\",\n            success: function success() {\n              (0, _utils.dec_notification_counter)();\n            }\n          });\n          break;\n        }\n\n      case \"background\":\n        {\n          (0, _utils.inc_notification_counter)();\n          break;\n        }\n\n      default:\n        {\n          (0, _utils.inc_notification_counter)();\n          break;\n        }\n    }\n  }\n\n  wc.alert = function (data) {\n    render(data);\n  };\n\n  wc.toast = function (data) {\n    render(data);\n  };\n\n  wc.background = function (data) {\n    render(data);\n  };\n\n  wc.masterDidChange = function () {\n    if (this.isMaster) {\n      connect();\n    } else {\n      disconnect();\n    }\n  };\n};\n\nexports[\"default\"] = _default;\n\n//# sourceURL=webpack:///./CTFd/themes/core/assets/js/events.js?");

/***/ }),

/***/ "./CTFd/themes/core/assets/js/ezq.js":
/*!*******************************************!*\
  !*** ./CTFd/themes/core/assets/js/ezq.js ***!
  \*******************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

;
eval("\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ezAlert = ezAlert;\nexports.ezToast = ezToast;\nexports.ezQuery = ezQuery;\nexports.ezProgressBar = ezProgressBar;\nexports.ezBadge = ezBadge;\nexports[\"default\"] = void 0;\n\n__webpack_require__(/*! bootstrap/js/dist/modal */ \"./node_modules/bootstrap/js/dist/modal.js\");\n\nvar _jquery = _interopRequireDefault(__webpack_require__(/*! jquery */ \"./node_modules/jquery/dist/jquery.js\"));\n\nvar _highlight = _interopRequireDefault(__webpack_require__(/*! highlight.js */ \"./node_modules/highlight.js/lib/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nvar modalTpl = '<div class=\"modal fade\" tabindex=\"-1\" role=\"dialog\">' + '  <div class=\"modal-dialog\" role=\"document\">' + '    <div class=\"modal-content\">' + '      <div class=\"modal-header\">' + '        <h5 class=\"modal-title\">{0}</h5>' + '        <button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\">' + '          <span aria-hidden=\"true\">&times;</span>' + \"        </button>\" + \"      </div>\" + '      <div class=\"modal-body\">' + \"      </div>\" + '      <div class=\"modal-footer\">' + \"      </div>\" + \"    </div>\" + \"  </div>\" + \"</div>\";\nvar toastTpl = '<div class=\"toast m-3\" role=\"alert\">' + '  <div class=\"toast-header\">' + '    <strong class=\"mr-auto\">{0}</strong>' + '    <button type=\"button\" class=\"ml-2 mb-1 close\" data-dismiss=\"toast\" aria-label=\"Close\">' + '      <span aria-hidden=\"true\">&times;</span>' + \"    </button>\" + \"  </div>\" + '  <div class=\"toast-body\">{1}</div>' + \"</div>\";\nvar progressTpl = '<div class=\"progress\">' + '  <div class=\"progress-bar progress-bar-success progress-bar-striped progress-bar-animated\" role=\"progressbar\" style=\"width: {0}%\">' + \"  </div>\" + \"</div>\";\nvar errorTpl = '<div class=\"alert alert-danger alert-dismissable\" role=\"alert\">\\n' + '  <span class=\"sr-only\">Error:</span>\\n' + \"  {0}\\n\" + '  <button type=\"button\" class=\"close\" data-dismiss=\"alert\" aria-label=\"Close\"><span aria-hidden=\"true\">×</span></button>\\n' + \"</div>\";\nvar successTpl = '<div class=\"alert alert-success alert-dismissable submit-row\" role=\"alert\">\\n' + \"  <strong>Success!</strong>\\n\" + \"  {0}\\n\" + '  <button type=\"button\" class=\"close\" data-dismiss=\"alert\" aria-label=\"Close\"><span aria-hidden=\"true\">×</span></button>\\n' + \"</div>\";\nvar buttonTpl = '<button type=\"button\" class=\"btn btn-primary\" data-dismiss=\"modal\">{0}</button>';\nvar noTpl = '<button type=\"button\" class=\"btn btn-danger\" data-dismiss=\"modal\">No</button>';\nvar yesTpl = '<button type=\"button\" class=\"btn btn-primary\" data-dismiss=\"modal\">Yes</button>';\n\nfunction ezAlert(args) {\n  var modal = modalTpl.format(args.title);\n  var obj = (0, _jquery[\"default\"])(modal);\n\n  if (typeof args.body === \"string\") {\n    obj.find(\".modal-body\").append(\"<p>\".concat(args.body, \"</p>\"));\n  } else {\n    obj.find(\".modal-body\").append((0, _jquery[\"default\"])(args.body));\n  }\n\n  var button = (0, _jquery[\"default\"])(buttonTpl.format(args.button));\n\n  if (args.success) {\n    (0, _jquery[\"default\"])(button).click(function () {\n      args.success();\n    });\n  }\n\n  if (args.large) {\n    obj.find(\".modal-dialog\").addClass(\"modal-lg\");\n  }\n\n  obj.find(\".modal-footer\").append(button); // Syntax highlighting\n\n  obj.find(\"pre code\").each(function (_idx) {\n    _highlight[\"default\"].highlightBlock(this);\n  });\n  (0, _jquery[\"default\"])(\"main\").append(obj);\n  obj.modal(\"show\");\n  (0, _jquery[\"default\"])(obj).on(\"hidden.bs.modal\", function () {\n    (0, _jquery[\"default\"])(this).modal(\"dispose\");\n  });\n  return obj;\n}\n\nfunction ezToast(args) {\n  var container_available = (0, _jquery[\"default\"])(\"#ezq--notifications-toast-container\").length;\n\n  if (!container_available) {\n    (0, _jquery[\"default\"])(\"body\").append((0, _jquery[\"default\"])(\"<div/>\").attr({\n      id: \"ezq--notifications-toast-container\"\n    }).css({\n      position: \"fixed\",\n      bottom: \"0\",\n      right: \"0\",\n      \"min-width\": \"20%\"\n    }));\n  }\n\n  var res = toastTpl.format(args.title, args.body);\n  var obj = (0, _jquery[\"default\"])(res);\n\n  if (args.onclose) {\n    (0, _jquery[\"default\"])(obj).find(\"button[data-dismiss=toast]\").click(function () {\n      args.onclose();\n    });\n  }\n\n  if (args.onclick) {\n    var body = (0, _jquery[\"default\"])(obj).find(\".toast-body\");\n    body.addClass(\"cursor-pointer\");\n    body.click(function () {\n      args.onclick();\n    });\n  }\n\n  var autohide = args.autohide !== false;\n  var animation = args.animation !== false;\n  var delay = args.delay || 10000; // 10 seconds\n\n  (0, _jquery[\"default\"])(\"#ezq--notifications-toast-container\").prepend(obj);\n  obj.toast({\n    autohide: autohide,\n    delay: delay,\n    animation: animation\n  });\n  obj.toast(\"show\");\n  return obj;\n}\n\nfunction ezQuery(args) {\n  var modal = modalTpl.format(args.title);\n  var obj = (0, _jquery[\"default\"])(modal);\n\n  if (typeof args.body === \"string\") {\n    obj.find(\".modal-body\").append(\"<p>\".concat(args.body, \"</p>\"));\n  } else {\n    obj.find(\".modal-body\").append((0, _jquery[\"default\"])(args.body));\n  }\n\n  var yes = (0, _jquery[\"default\"])(yesTpl);\n  var no = (0, _jquery[\"default\"])(noTpl);\n  obj.find(\".modal-footer\").append(no);\n  obj.find(\".modal-footer\").append(yes); // Syntax highlighting\n\n  obj.find(\"pre code\").each(function (_idx) {\n    _highlight[\"default\"].highlightBlock(this);\n  });\n  (0, _jquery[\"default\"])(\"main\").append(obj);\n  (0, _jquery[\"default\"])(obj).on(\"hidden.bs.modal\", function () {\n    (0, _jquery[\"default\"])(this).modal(\"dispose\");\n  });\n  (0, _jquery[\"default\"])(yes).click(function () {\n    args.success();\n  });\n  obj.modal(\"show\");\n  return obj;\n}\n\nfunction ezProgressBar(args) {\n  if (args.target) {\n    var _obj = (0, _jquery[\"default\"])(args.target);\n\n    var pbar = _obj.find(\".progress-bar\");\n\n    pbar.css(\"width\", args.width + \"%\");\n    return _obj;\n  }\n\n  var progress = progressTpl.format(args.width);\n  var modal = modalTpl.format(args.title);\n  var obj = (0, _jquery[\"default\"])(modal);\n  obj.find(\".modal-body\").append((0, _jquery[\"default\"])(progress));\n  (0, _jquery[\"default\"])(\"main\").append(obj);\n  return obj.modal(\"show\");\n}\n\nfunction ezBadge(args) {\n  var mapping = {\n    success: successTpl,\n    error: errorTpl\n  };\n  var tpl = mapping[args.type].format(args.body);\n  return (0, _jquery[\"default\"])(tpl);\n}\n\nvar ezq = {\n  ezAlert: ezAlert,\n  ezToast: ezToast,\n  ezQuery: ezQuery,\n  ezProgressBar: ezProgressBar,\n  ezBadge: ezBadge\n};\nvar _default = ezq;\nexports[\"default\"] = _default;\n\n//# sourceURL=webpack:///./CTFd/themes/core/assets/js/ezq.js?");

/***/ }),

/***/ "./CTFd/themes/core/assets/js/fetch.js":
/*!*********************************************!*\
  !*** ./CTFd/themes/core/assets/js/fetch.js ***!
  \*********************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

;
eval("\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\n__webpack_require__(/*! whatwg-fetch */ \"./node_modules/whatwg-fetch/fetch.js\");\n\nvar _config = _interopRequireDefault(__webpack_require__(/*! ./config */ \"./CTFd/themes/core/assets/js/config.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nvar fetch = window.fetch;\n\nvar _default = function _default(url, options) {\n  if (options === undefined) {\n    options = {\n      method: \"GET\",\n      credentials: \"same-origin\",\n      headers: {}\n    };\n  }\n\n  url = _config[\"default\"].urlRoot + url;\n\n  if (options.headers === undefined) {\n    options.headers = {};\n  }\n\n  options.credentials = \"same-origin\";\n  options.headers[\"Accept\"] = \"application/json\";\n  options.headers[\"Content-Type\"] = \"application/json\";\n  options.headers[\"CSRF-Token\"] = _config[\"default\"].csrfNonce;\n  return fetch(url, options);\n};\n\nexports[\"default\"] = _default;\n\n//# sourceURL=webpack:///./CTFd/themes/core/assets/js/fetch.js?");

/***/ }),

/***/ "./CTFd/themes/core/assets/js/pages/main.js":
/*!**************************************************!*\
  !*** ./CTFd/themes/core/assets/js/pages/main.js ***!
  \**************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

;
eval("\n\nvar _CTFd = _interopRequireDefault(__webpack_require__(/*! ../CTFd */ \"./CTFd/themes/core/assets/js/CTFd.js\"));\n\nvar _jquery = _interopRequireDefault(__webpack_require__(/*! jquery */ \"./node_modules/jquery/dist/jquery.js\"));\n\nvar _dayjs = _interopRequireDefault(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\"));\n\nvar _advancedFormat = _interopRequireDefault(__webpack_require__(/*! dayjs/plugin/advancedFormat */ \"./node_modules/dayjs/plugin/advancedFormat.js\"));\n\nvar _nunjucks = _interopRequireDefault(__webpack_require__(/*! nunjucks */ \"./node_modules/nunjucks/browser/nunjucks.js\"));\n\nvar _howler = __webpack_require__(/*! howler */ \"./node_modules/howler/dist/howler.js\");\n\nvar _events = _interopRequireDefault(__webpack_require__(/*! ../events */ \"./CTFd/themes/core/assets/js/events.js\"));\n\nvar _config = _interopRequireDefault(__webpack_require__(/*! ../config */ \"./CTFd/themes/core/assets/js/config.js\"));\n\nvar _styles = _interopRequireDefault(__webpack_require__(/*! ../styles */ \"./CTFd/themes/core/assets/js/styles.js\"));\n\nvar _times = _interopRequireDefault(__webpack_require__(/*! ../times */ \"./CTFd/themes/core/assets/js/times.js\"));\n\nvar _helpers = _interopRequireDefault(__webpack_require__(/*! ../helpers */ \"./CTFd/themes/core/assets/js/helpers.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\n_dayjs[\"default\"].extend(_advancedFormat[\"default\"]);\n\n_CTFd[\"default\"].init(window.init);\n\nwindow.CTFd = _CTFd[\"default\"];\nwindow.helpers = _helpers[\"default\"];\nwindow.$ = _jquery[\"default\"];\nwindow.dayjs = _dayjs[\"default\"];\nwindow.nunjucks = _nunjucks[\"default\"];\nwindow.Howl = _howler.Howl;\n(0, _jquery[\"default\"])(function () {\n  (0, _styles[\"default\"])();\n  (0, _times[\"default\"])();\n  (0, _events[\"default\"])(_config[\"default\"].urlRoot);\n});\n\n//# sourceURL=webpack:///./CTFd/themes/core/assets/js/pages/main.js?");

/***/ }),

/***/ "./CTFd/themes/core/assets/js/patch.js":
/*!*********************************************!*\
  !*** ./CTFd/themes/core/assets/js/patch.js ***!
  \*********************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

;
eval("\n\nvar _q = _interopRequireDefault(__webpack_require__(/*! q */ \"./node_modules/q/q.js\"));\n\nvar _api = __webpack_require__(/*! ./api */ \"./CTFd/themes/core/assets/js/api.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction mergeQueryParams(parameters, queryParameters) {\n  return _objectSpread(_objectSpread({}, parameters), queryParameters);\n}\n\nfunction serializeQueryParams(parameters) {\n  var str = [];\n\n  for (var p in parameters) {\n    if (parameters.hasOwnProperty(p)) {\n      str.push(encodeURIComponent(p) + \"=\" + encodeURIComponent(parameters[p]));\n    }\n  }\n\n  return str.join(\"&\");\n}\n\n_api.API.prototype.requestRaw = function (method, url, parameters, body, headers, queryParameters, form, deferred) {\n  var queryParams = queryParameters && Object.keys(queryParameters).length ? serializeQueryParams(queryParameters) : null;\n  var urlWithParams = url + (queryParams ? \"?\" + queryParams : \"\");\n\n  if (body && !Object.keys(body).length) {\n    body = undefined;\n  }\n\n  fetch(urlWithParams, {\n    method: method,\n    headers: headers,\n    body: body\n  }).then(function (response) {\n    return response.json();\n  }).then(function (body) {\n    deferred.resolve(body);\n  })[\"catch\"](function (error) {\n    deferred.reject(error);\n  });\n};\n\n_api.API.prototype.patch_user_public = function (parameters, body) {\n  if (parameters === undefined) {\n    parameters = {};\n  }\n\n  var deferred = _q[\"default\"].defer();\n\n  var domain = this.domain,\n      path = \"/users/{user_id}\";\n  var queryParameters = {},\n      headers = {},\n      form = {};\n  headers[\"Accept\"] = [\"application/json\"];\n  headers[\"Content-Type\"] = [\"application/json\"];\n  path = path.replace(\"{user_id}\", parameters[\"userId\"]);\n\n  if (parameters[\"userId\"] === undefined) {\n    deferred.reject(new Error(\"Missing required  parameter: userId\"));\n    return deferred.promise;\n  }\n\n  this.request(\"PATCH\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n  return deferred.promise;\n};\n\n_api.API.prototype.patch_user_private = function (parameters, body) {\n  if (parameters === undefined) {\n    parameters = {};\n  }\n\n  var deferred = _q[\"default\"].defer();\n\n  var domain = this.domain,\n      path = \"/users/me\";\n  var headers = {},\n      form = {};\n  headers[\"Accept\"] = [\"application/json\"];\n  headers[\"Content-Type\"] = [\"application/json\"];\n  this.request(\"PATCH\", domain + path, parameters, body, headers, {}, form, deferred);\n  return deferred.promise;\n};\n\n_api.API.prototype.post_unlock_list = function (parameters, body) {\n  var deferred = _q[\"default\"].defer();\n\n  var domain = this.domain,\n      path = \"/unlocks\";\n  var headers = {},\n      form = {};\n  headers[\"Accept\"] = [\"application/json\"];\n  headers[\"Content-Type\"] = [\"application/json\"];\n  this.request(\"POST\", domain + path, parameters, body, headers, {}, form, deferred);\n  return deferred.promise;\n};\n\n_api.API.prototype.post_notification_list = function (parameters, body) {\n  if (parameters === undefined) {\n    parameters = {};\n  }\n\n  var deferred = _q[\"default\"].defer();\n\n  var domain = this.domain,\n      path = \"/notifications\";\n  var queryParameters = {},\n      headers = {},\n      form = {};\n  headers[\"Accept\"] = [\"application/json\"];\n  headers[\"Content-Type\"] = [\"application/json\"];\n  this.request(\"POST\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n  return deferred.promise;\n};\n\n_api.API.prototype.post_files_list = function (parameters, body) {\n  var deferred = _q[\"default\"].defer();\n\n  var domain = this.domain,\n      path = \"/files\";\n  var queryParameters = {},\n      headers = {},\n      form = {};\n  headers[\"Accept\"] = [\"application/json\"];\n  headers[\"Content-Type\"] = [\"application/json\"];\n  this.requestRaw(\"POST\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n  return deferred.promise;\n};\n\n_api.API.prototype.patch_config = function (parameters, body) {\n  if (parameters === undefined) {\n    parameters = {};\n  }\n\n  var deferred = _q[\"default\"].defer();\n\n  var domain = this.domain,\n      path = \"/configs/{config_key}\";\n  var queryParameters = {},\n      headers = {},\n      form = {};\n  headers[\"Accept\"] = [\"application/json\"];\n  headers[\"Content-Type\"] = [\"application/json\"];\n  path = path.replace(\"{config_key}\", parameters[\"configKey\"]);\n\n  if (parameters[\"configKey\"] === undefined) {\n    deferred.reject(new Error(\"Missing required  parameter: configKey\"));\n    return deferred.promise;\n  }\n\n  this.request(\"PATCH\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n  return deferred.promise;\n};\n\n_api.API.prototype.patch_config_list = function (parameters, body) {\n  if (parameters === undefined) {\n    parameters = {};\n  }\n\n  var deferred = _q[\"default\"].defer();\n\n  var domain = this.domain,\n      path = \"/configs\";\n  var queryParameters = {},\n      headers = {},\n      form = {};\n  headers[\"Accept\"] = [\"application/json\"];\n  headers[\"Content-Type\"] = [\"application/json\"];\n  queryParameters = mergeQueryParams(parameters, queryParameters);\n  this.request(\"PATCH\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n  return deferred.promise;\n};\n\n_api.API.prototype.post_tag_list = function (parameters, body) {\n  if (parameters === undefined) {\n    parameters = {};\n  }\n\n  var deferred = _q[\"default\"].defer();\n\n  var domain = this.domain,\n      path = \"/tags\";\n  var queryParameters = {},\n      headers = {},\n      form = {};\n  headers[\"Accept\"] = [\"application/json\"];\n  headers[\"Content-Type\"] = [\"application/json\"];\n  queryParameters = mergeQueryParams(parameters, queryParameters);\n  this.request(\"POST\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n  return deferred.promise;\n};\n\n_api.API.prototype.patch_team_public = function (parameters, body) {\n  if (parameters === undefined) {\n    parameters = {};\n  }\n\n  var deferred = _q[\"default\"].defer();\n\n  var domain = this.domain,\n      path = \"/teams/{team_id}\";\n  var queryParameters = {},\n      headers = {},\n      form = {};\n  headers[\"Accept\"] = [\"application/json\"];\n  headers[\"Content-Type\"] = [\"application/json\"];\n  path = path.replace(\"{team_id}\", parameters[\"teamId\"]);\n\n  if (parameters[\"teamId\"] === undefined) {\n    deferred.reject(new Error(\"Missing required  parameter: teamId\"));\n    return deferred.promise;\n  }\n\n  queryParameters = mergeQueryParams(parameters, queryParameters);\n  this.request(\"PATCH\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n  return deferred.promise;\n};\n\n_api.API.prototype.post_challenge_attempt = function (parameters, body) {\n  if (parameters === undefined) {\n    parameters = {};\n  }\n\n  var deferred = _q[\"default\"].defer();\n\n  var domain = this.domain,\n      path = \"/challenges/attempt\";\n  var queryParameters = {},\n      headers = {},\n      form = {};\n  headers[\"Accept\"] = [\"application/json\"];\n  headers[\"Content-Type\"] = [\"application/json\"];\n  queryParameters = mergeQueryParams(parameters, queryParameters);\n  this.request(\"POST\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n  return deferred.promise;\n};\n\n_api.API.prototype.get_hint = function (parameters) {\n  if (parameters === undefined) {\n    parameters = {};\n  }\n\n  var deferred = _q[\"default\"].defer();\n\n  var domain = this.domain,\n      path = \"/hints/{hint_id}\";\n  var body = {},\n      queryParameters = {},\n      headers = {},\n      form = {};\n  headers[\"Accept\"] = [\"application/json\"];\n  headers[\"Content-Type\"] = [\"application/json\"];\n  path = path.replace(\"{hint_id}\", parameters[\"hintId\"]);\n\n  if (parameters[\"hintId\"] === undefined) {\n    deferred.reject(new Error(\"Missing required  parameter: hintId\"));\n    return deferred.promise;\n  }\n\n  delete parameters[\"hintId\"];\n  queryParameters = mergeQueryParams(parameters, queryParameters);\n  this.request(\"GET\", domain + path, parameters, body, headers, queryParameters, form, deferred);\n  return deferred.promise;\n};\n\n//# sourceURL=webpack:///./CTFd/themes/core/assets/js/patch.js?");

/***/ }),

/***/ "./CTFd/themes/core/assets/js/styles.js":
/*!**********************************************!*\
  !*** ./CTFd/themes/core/assets/js/styles.js ***!
  \**********************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

;
eval("\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\n__webpack_require__(/*! bootstrap/dist/js/bootstrap.bundle */ \"./node_modules/bootstrap/dist/js/bootstrap.bundle.js\");\n\nvar _jquery = _interopRequireDefault(__webpack_require__(/*! jquery */ \"./node_modules/jquery/dist/jquery.js\"));\n\nvar _highlight = _interopRequireDefault(__webpack_require__(/*! highlight.js */ \"./node_modules/highlight.js/lib/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nvar _default = function _default() {\n  // TODO: This is kind of a hack to mimic a React-like state construct.\n  // It should be removed once we have a real front-end framework in place.\n  (0, _jquery[\"default\"])(\":input\").each(function () {\n    (0, _jquery[\"default\"])(this).data(\"initial\", (0, _jquery[\"default\"])(this).val());\n  });\n  (0, _jquery[\"default\"])(\".form-control\").bind({\n    focus: function focus() {\n      (0, _jquery[\"default\"])(this).removeClass(\"input-filled-invalid\");\n      (0, _jquery[\"default\"])(this).addClass(\"input-filled-valid\");\n    },\n    blur: function blur() {\n      if ((0, _jquery[\"default\"])(this).val() === \"\") {\n        (0, _jquery[\"default\"])(this).removeClass(\"input-filled-invalid\");\n        (0, _jquery[\"default\"])(this).removeClass(\"input-filled-valid\");\n      }\n    }\n  });\n  (0, _jquery[\"default\"])(\".form-control\").each(function () {\n    if ((0, _jquery[\"default\"])(this).val()) {\n      (0, _jquery[\"default\"])(this).addClass(\"input-filled-valid\");\n    }\n  });\n  (0, _jquery[\"default\"])(\".page-select\").change(function () {\n    var url = new URL(window.location);\n    url.searchParams.set(\"page\", this.value);\n    window.location.href = url.toString();\n  });\n  (0, _jquery[\"default\"])('[data-toggle=\"tooltip\"]').tooltip();\n  (0, _jquery[\"default\"])(function () {\n    // Syntax highlighting\n    document.querySelectorAll(\"pre code\").forEach(function (block) {\n      _highlight[\"default\"].highlightBlock(block);\n    });\n  });\n};\n\nexports[\"default\"] = _default;\n\n//# sourceURL=webpack:///./CTFd/themes/core/assets/js/styles.js?");

/***/ }),

/***/ "./CTFd/themes/core/assets/js/times.js":
/*!*********************************************!*\
  !*** ./CTFd/themes/core/assets/js/times.js ***!
  \*********************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

;
eval("\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _dayjs = _interopRequireDefault(__webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\"));\n\nvar _advancedFormat = _interopRequireDefault(__webpack_require__(/*! dayjs/plugin/advancedFormat */ \"./node_modules/dayjs/plugin/advancedFormat.js\"));\n\nvar _jquery = _interopRequireDefault(__webpack_require__(/*! jquery */ \"./node_modules/jquery/dist/jquery.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\n_dayjs[\"default\"].extend(_advancedFormat[\"default\"]);\n\nvar _default = function _default() {\n  (0, _jquery[\"default\"])(\"[data-time]\").each(function (i, elem) {\n    var $elem = (0, _jquery[\"default\"])(elem);\n    var time = $elem.data(\"time\");\n    var format = $elem.data(\"time-format\") || \"MMMM Do, h:mm:ss A\";\n    elem.innerText = (0, _dayjs[\"default\"])(time).format(format);\n  });\n};\n\nexports[\"default\"] = _default;\n\n//# sourceURL=webpack:///./CTFd/themes/core/assets/js/times.js?");

/***/ }),

/***/ "./CTFd/themes/core/assets/js/utils.js":
/*!*********************************************!*\
  !*** ./CTFd/themes/core/assets/js/utils.js ***!
  \*********************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

;
eval("\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.WindowController = WindowController;\nexports.colorHash = colorHash;\nexports.cumulativeSum = cumulativeSum;\nexports.init_notification_counter = init_notification_counter;\nexports.set_notification_counter = set_notification_counter;\nexports.inc_notification_counter = inc_notification_counter;\nexports.dec_notification_counter = dec_notification_counter;\nexports.clear_notification_counter = clear_notification_counter;\nexports.copyToClipboard = copyToClipboard;\nexports.makeSortableTables = makeSortableTables;\nexports.getScript = getScript;\nexports.createHtmlNode = createHtmlNode;\nexports.htmlEntities = htmlEntities;\n\nvar _jquery = _interopRequireDefault(__webpack_require__(/*! jquery */ \"./node_modules/jquery/dist/jquery.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\n_jquery[\"default\"].fn.serializeJSON = function (omit_nulls) {\n  var params = {};\n  var form = (0, _jquery[\"default\"])(this);\n  var values = form.serializeArray();\n  values = values.concat(form.find(\"input[type=checkbox]:checked\").map(function () {\n    return {\n      name: this.name,\n      value: true\n    };\n  }).get());\n  values = values.concat(form.find(\"input[type=checkbox]:not(:checked)\").map(function () {\n    return {\n      name: this.name,\n      value: false\n    };\n  }).get());\n  values.map(function (x) {\n    if (omit_nulls) {\n      if (x.value !== null && x.value !== \"\") {\n        params[x.name] = x.value;\n      } else {\n        var input = form.find(\":input[name='\".concat(x.name, \"']\"));\n\n        if (input.data(\"initial\") !== input.val()) {\n          params[x.name] = x.value;\n        }\n      }\n    } else {\n      params[x.name] = x.value;\n    }\n  });\n  return params;\n}; //http://stackoverflow.com/a/2648463 - wizardry!\n\n\nString.prototype.format = String.prototype.f = function () {\n  var s = this,\n      i = arguments.length;\n\n  while (i--) {\n    s = s.replace(new RegExp(\"\\\\{\" + i + \"\\\\}\", \"gm\"), arguments[i]);\n  }\n\n  return s;\n}; //http://stackoverflow.com/a/7616484\n\n\nString.prototype.hashCode = function () {\n  var hash = 0,\n      i,\n      chr,\n      len;\n  if (this.length == 0) return hash;\n\n  for (i = 0, len = this.length; i < len; i++) {\n    chr = this.charCodeAt(i);\n    hash = (hash << 5) - hash + chr;\n    hash |= 0; // Convert to 32bit integer\n  }\n\n  return hash;\n}; // https://gist.github.com/neilj/4146038\n// https://fastmail.blog/2012/11/26/inter-tab-communication-using-local-storage/\n\n\nfunction WindowController() {\n  this.id = Math.random();\n  this.isMaster = false;\n  this.others = {};\n  window.addEventListener(\"storage\", this, false);\n  window.addEventListener(\"unload\", this, false);\n  this.broadcast(\"hello\");\n  var that = this;\n\n  var check = function check() {\n    that.check();\n    that._checkTimeout = setTimeout(check, 9000);\n  };\n\n  var ping = function ping() {\n    that.sendPing();\n    that._pingTimeout = setTimeout(ping, 17000);\n  };\n\n  this._checkTimeout = setTimeout(check, 500);\n  this._pingTimeout = setTimeout(ping, 17000);\n}\n\nWindowController.prototype.destroy = function () {\n  clearTimeout(this._pingTimeout);\n  clearTimeout(this._checkTimeout);\n  window.removeEventListener(\"storage\", this, false);\n  window.removeEventListener(\"unload\", this, false);\n  this.broadcast(\"bye\");\n};\n\nWindowController.prototype.handleEvent = function (event) {\n  if (event.type === \"unload\") {\n    this.destroy();\n  } else if (event.key === \"broadcast\") {\n    try {\n      var data = JSON.parse(event.newValue);\n\n      if (data.id !== this.id) {\n        this[data.type](data);\n      }\n    } catch (error) {\n      // eslint-disable-next-line no-console\n      console.log(error);\n    }\n  }\n};\n\nWindowController.prototype.sendPing = function () {\n  this.broadcast(\"ping\");\n};\n\nWindowController.prototype.hello = function (event) {\n  this.ping(event);\n\n  if (event.id < this.id) {\n    this.check();\n  } else {\n    this.sendPing();\n  }\n};\n\nWindowController.prototype.ping = function (event) {\n  this.others[event.id] = +new Date();\n};\n\nWindowController.prototype.bye = function (event) {\n  delete this.others[event.id];\n  this.check();\n};\n\nWindowController.prototype.check = function (_event) {\n  var now = +new Date(),\n      takeMaster = true,\n      id;\n\n  for (id in this.others) {\n    if (this.others[id] + 23000 < now) {\n      delete this.others[id];\n    } else if (id < this.id) {\n      takeMaster = false;\n    }\n  }\n\n  if (this.isMaster !== takeMaster) {\n    this.isMaster = takeMaster;\n    this.masterDidChange();\n  }\n};\n\nWindowController.prototype.masterDidChange = function () {};\n\nWindowController.prototype.broadcast = function (type, data) {\n  var event = {\n    id: this.id,\n    type: type\n  };\n\n  for (var x in data) {\n    event[x] = data[x];\n  }\n\n  try {\n    localStorage.setItem(\"broadcast\", JSON.stringify(event));\n  } catch (error) {\n    // eslint-disable-next-line no-console\n    console.log(error);\n  }\n}; // https://gist.github.com/0x263b/2bdd90886c2036a1ad5bcf06d6e6fb37\n\n\nfunction colorHash(str) {\n  var hash = 0;\n\n  for (var i = 0; i < str.length; i++) {\n    hash = str.charCodeAt(i) + ((hash << 5) - hash);\n    hash = hash & hash;\n  } // Range calculation\n  // diff = max - min;\n  // x = ((hash % diff) + diff) % diff;\n  // return x + min;\n  // Calculate HSL values\n  // Range from 0 to 360\n\n\n  var h = (hash % 360 + 360) % 360; // Range from 75 to 100\n\n  var s = (hash % 25 + 25) % 25 + 75; // Range from 40 to 60\n\n  var l = (hash % 20 + 20) % 20 + 40;\n  return \"hsl(\".concat(h, \", \").concat(s, \"%, \").concat(l, \"%)\");\n}\n\nfunction cumulativeSum(arr) {\n  var result = arr.concat();\n\n  for (var i = 0; i < arr.length; i++) {\n    result[i] = arr.slice(0, i + 1).reduce(function (p, i) {\n      return p + i;\n    });\n  }\n\n  return result;\n}\n\nvar storage = window.localStorage;\nvar counter_key = \"unread_notifications\";\n\nfunction init_notification_counter() {\n  var count = storage.getItem(counter_key);\n\n  if (count === null) {\n    storage.setItem(counter_key, 0);\n  } else {\n    if (count > 0) {\n      (0, _jquery[\"default\"])(\".badge-notification\").text(count);\n    }\n  }\n}\n\nfunction set_notification_counter(count) {\n  storage.setItem(counter_key, count);\n}\n\nfunction inc_notification_counter() {\n  var count = storage.getItem(counter_key) || 0;\n  storage.setItem(counter_key, ++count);\n  (0, _jquery[\"default\"])(\".badge-notification\").text(count);\n}\n\nfunction dec_notification_counter() {\n  var count = storage.getItem(counter_key) || 0;\n\n  if (count > 0) {\n    storage.setItem(counter_key, --count);\n    (0, _jquery[\"default\"])(\".badge-notification\").text(count);\n  } // Always clear if count is 0\n\n\n  if (count == 0) {\n    clear_notification_counter();\n  }\n}\n\nfunction clear_notification_counter() {\n  storage.setItem(counter_key, 0);\n  (0, _jquery[\"default\"])(\".badge-notification\").empty();\n}\n\nfunction copyToClipboard(event, selector) {\n  // Select element\n  (0, _jquery[\"default\"])(selector).select(); // Copy to clipboard\n\n  document.execCommand(\"copy\"); // Show tooltip to user\n\n  (0, _jquery[\"default\"])(event.target).tooltip({\n    title: \"Copied!\",\n    trigger: \"manual\"\n  });\n  (0, _jquery[\"default\"])(event.target).tooltip(\"show\");\n  setTimeout(function () {\n    (0, _jquery[\"default\"])(event.target).tooltip(\"hide\");\n  }, 1500);\n}\n\nfunction makeSortableTables() {\n  (0, _jquery[\"default\"])(\"th.sort-col\").append(\" <i class=\\\"fas fa-sort\\\"></i>\");\n  (0, _jquery[\"default\"])(\"th.sort-col\").click(function () {\n    var table = (0, _jquery[\"default\"])(this).parents(\"table\").eq(0);\n    var rows = table.find(\"tr:gt(0)\").toArray().sort(comparer((0, _jquery[\"default\"])(this).index()));\n    this.asc = !this.asc;\n\n    if (!this.asc) {\n      rows = rows.reverse();\n    }\n\n    for (var i = 0; i < rows.length; i++) {\n      table.append(rows[i]);\n    }\n  });\n\n  function comparer(index) {\n    return function (a, b) {\n      var valA = getCellValue(a, index),\n          valB = getCellValue(b, index);\n      return _jquery[\"default\"].isNumeric(valA) && _jquery[\"default\"].isNumeric(valB) ? valA - valB : valA.toString().localeCompare(valB);\n    };\n  }\n\n  function getCellValue(row, index) {\n    return (0, _jquery[\"default\"])(row).children(\"td\").eq(index).text();\n  }\n}\n\nfunction getScript(src) {\n  var p = new Promise(function (resolve, reject) {\n    var script = document.createElement(\"script\");\n    document.body.appendChild(script);\n    script.onload = resolve;\n    script.onerror = reject;\n    script.async = true;\n    script.src = src;\n  });\n  return p;\n}\n\nfunction createHtmlNode(html) {\n  var template = document.createElement(\"template\");\n  template.innerHTML = html.trim();\n  return template.content.firstChild;\n}\n\nfunction htmlEntities(string) {\n  return (0, _jquery[\"default\"])(\"<div/>\").text(string).html();\n}\n\n//# sourceURL=webpack:///./CTFd/themes/core/assets/js/utils.js?");

/***/ }),

/***/ 0:
/*!**********************!*\
  !*** util (ignored) ***!
  \**********************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("/* (ignored) */\n\n//# sourceURL=webpack:///util_(ignored)?");

/***/ }),

/***/ 1:
/*!**********************!*\
  !*** util (ignored) ***!
  \**********************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("/* (ignored) */\n\n//# sourceURL=webpack:///util_(ignored)?");

/***/ })

}]);
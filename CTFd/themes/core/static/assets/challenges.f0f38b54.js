import{m as l,C as a,h,T as o,d as c,M as d,a as u}from"./index.b6be5691.js";function r(e){let s=new DOMParser().parseFromString(e,"text/html");return s.querySelectorAll('a[href*="://"]').forEach(i=>{i.setAttribute("target","_blank")}),s.documentElement.outerHTML}window.Alpine=l;l.store("challenge",{data:{view:""}});l.data("Hint",()=>({id:null,html:null,async showHint(e){if(e.target.open){let t=await a.pages.challenge.loadHint(this.id);if(t.errors){e.target.open=!1,a._functions.challenge.displayUnlockError(t);return}let s=t.data;if(s.content)this.html=r(s.html);else if(await a.pages.challenge.displayUnlock(this.id)){let i=await a.pages.challenge.loadUnlock(this.id);if(i.success){let g=(await a.pages.challenge.loadHint(this.id)).data;this.html=r(g.html)}else e.target.open=!1,a._functions.challenge.displayUnlockError(i)}else e.target.open=!1}}}));l.data("Challenge",()=>({id:null,next_id:null,submission:"",tab:null,solves:[],submissions:[],solution:null,response:null,share_url:null,max_attempts:0,attempts:0,ratingValue:0,selectedRating:0,ratingReview:"",ratingSubmitted:!1,async init(){h()},getStyles(){let e={"modal-dialog":!0};try{switch(a.config.themeSettings.challenge_window_size){case"sm":e["modal-sm"]=!0;break;case"lg":e["modal-lg"]=!0;break;case"xl":e["modal-xl"]=!0;break;default:break}}catch(t){console.log("Error processing challenge_window_size"),console.log(t)}return e},async init(){h()},async showChallenge(){new o(this.$el).show()},async showSolves(){this.solves=await a.pages.challenge.loadSolves(this.id),this.solves.forEach(e=>(e.date=c(e.date).format("MMMM Do, h:mm:ss A"),e)),new o(this.$el).show()},async showSubmissions(){let e=await a.pages.users.userSubmissions("me",this.id);this.submissions=e.data,this.submissions.forEach(t=>(t.date=c(t.date).format("MMMM Do, h:mm:ss A"),t)),new o(this.$el).show()},getSolutionId(){return l.store("challenge").data.solution_id},async showSolution(){let e=this.getSolutionId();a._functions.challenge.displaySolution=t=>{this.solution=t.html,new o(this.$el).show()},await a.pages.challenge.displaySolution(e)},getNextId(){return l.store("challenge").data.next_id},async nextChallenge(){let e=d.getOrCreateInstance("[x-ref='challengeWindow']");e._element.addEventListener("hidden.bs.modal",t=>{l.nextTick(()=>{this.$dispatch("load-challenge",this.getNextId())})},{once:!0}),e.hide()},async getShareUrl(){let e={type:"solve",challenge_id:this.id};const n=(await(await a.fetch("/api/v1/shares",{method:"POST",body:JSON.stringify(e)})).json()).data.url;this.share_url=n},copyShareUrl(){navigator.clipboard.writeText(this.share_url);let e=u.getOrCreateInstance(this.$el);e.enable(),e.show(),setTimeout(()=>{e.hide(),e.disable()},2e3)},async submitChallenge(){this.response=await a.pages.challenge.submitChallenge(this.id,this.submission),await this.renderSubmissionResponse()},async renderSubmissionResponse(){this.response.data.status==="correct"&&(this.submission=""),this.max_attempts>0&&this.response.data.status!="already_solved"&&this.response.data.status!="ratelimited"&&(this.attempts+=1),this.$dispatch("load-challenges")},async submitRating(){(await a.pages.challenge.submitRating(this.id,this.selectedRating,this.ratingReview)).value?(this.ratingValue=this.selectedRating,this.ratingSubmitted=!0):alert("Error submitting rating")}}));l.data("ChallengeBoard",()=>({loaded:!1,challenges:[],challenge:null,async init(){if(this.challenges=await a.pages.challenges.getChallenges(),this.loaded=!0,window.location.hash){let e=decodeURIComponent(window.location.hash.substring(1)),t=e.lastIndexOf("-");if(t>=0){let n=[e.slice(0,t),e.slice(t+1)][1];await this.loadChallenge(n)}}},getCategories(){const e=[];this.challenges.forEach(t=>{const{category:s}=t;e.includes(s)||e.push(s)});try{const t=a.config.themeSettings.challenge_category_order;if(t){const s=new Function(`return (${t})`);e.sort(s())}}catch(t){console.log("Error running challenge_category_order function"),console.log(t)}return e},getChallenges(e){let t=this.challenges;e!==null&&(t=this.challenges.filter(s=>s.category===e));try{const s=a.config.themeSettings.challenge_order;if(s){const n=new Function(`return (${s})`);t.sort(n())}}catch(s){console.log("Error running challenge_order function"),console.log(s)}return t},async loadChallenges(){this.challenges=await a.pages.challenges.getChallenges()},async loadChallenge(e){await a.pages.challenge.displayChallenge(e,t=>{t.data.view=r(t.data.view),l.store("challenge").data=t.data,l.nextTick(()=>{let s=d.getOrCreateInstance("[x-ref='challengeWindow']");s._element.addEventListener("hidden.bs.modal",n=>{history.replaceState(null,null," ")},{once:!0}),s.show(),history.replaceState(null,null,`#${t.data.name}-${e}`)})})}}));l.start();

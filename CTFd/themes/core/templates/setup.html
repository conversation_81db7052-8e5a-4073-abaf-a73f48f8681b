{% extends "base.html" %}

{% block stylesheets %}
{{ super() }}
<style>
.card-radio:checked + .card {
	background-color: transparent !important;
	border-color: #a3d39c;
	box-shadow: 0 0 0 0.1rem #a3d39c;
	transition: background-color 0.3s, border-color 0.3s;
}
.card-radio:checked + .card .card-radio-clone{
	visibility: visible !important;
}
.card:hover {
	cursor: pointer;
}
</style>
{% endblock %}

{% block content %}
	<div class="jumbotron">
		<div class="container">
			<h1>{% trans %}Setup{% endtrans %}</h1>
		</div>
	</div>
	<div class="container">
		<div class="col-md-8 offset-md-2">
			{% include "components/errors.html" %}

			{% with form = Forms.setup.SetupForm() %}
			<form 
				x-data="SetupForm"
				@submit="submitSetup"
				method="post" 
				accept-charset="utf-8" 
				autocomplete="off" 
				role="form" class="form-horizontal" 
				id="setup-form" 
				enctype="multipart/form-data"
			>
				<ul class="nav nav-pills nav-fill mb-4">
					<li class="nav-item">
						<a class="nav-link active" data-bs-toggle="pill" data-bs-target="#general" role="button">{% trans %}General{% endtrans %}</a>
					</li>
					<li class="nav-item">
						<a class="nav-link" data-bs-toggle="pill" data-bs-target="#mode" role="button">{% trans %}Mode{% endtrans %}</a>
					</li>
					<li class="nav-item">
						<a class="nav-link" data-bs-toggle="pill" data-bs-target="#settings" role="button">{% trans %}Settings{% endtrans %}</a>
					</li>
					<li class="nav-item">
						<a class="nav-link" data-bs-toggle="pill" data-bs-target="#administration" role="button">{% trans %}Administration{% endtrans %}</a>
					</li>
					<li class="nav-item">
						<a class="nav-link" data-bs-toggle="pill" data-bs-target="#style" role="button">{% trans %}Style{% endtrans %}</a>
					</li>
					<li class="nav-item">
						<a class="nav-link" data-bs-toggle="pill" data-bs-target="#datetime" role="button">{% trans %}Date &amp; Time{% endtrans %}</a>
					</li>
					<li class="nav-item">
						<a class="nav-link" data-bs-toggle="pill" data-bs-target="#integrations" role="button">{% trans %}Integrations{% endtrans %}</a>
					</li>
				</ul>

				<div class="tab-content">
					<div class="tab-pane fade show active" id="general" role="tabpanel">
						<div class="mb-3">
							<b>{{ form.ctf_name.label }}</b>
							{{ form.ctf_name(class="form-control") }}
							<small class="form-text text-muted">
								{{ form.ctf_name.description }}
							</small>
						</div>

						<div class="mb-3">
							<b>{{ form.ctf_description.label }}</b>
							{{ form.ctf_description(class="form-control", rows="5") }}
							<small class="form-text text-muted">
								{{ form.ctf_description.description }}
							</small>
						</div>

						<div class="text-right float-end">
							<button type="button" class="btn btn-primary btn-outlined tab-next" data-href="#mode" @click="switchTab">
								{% trans %}Next{% endtrans %}
							</button>
						</div>
					</div>
					<div class="tab-pane fade" id="mode" role="tabpanel">
						<div class="mb-3">
							<b>{{ form.user_mode.label }}</b>
							<br>
							<small class="form-text text-muted">
								{{ form.user_mode.description }}
							</small>

							<div class="row pt-3">
							{% for radio in form.user_mode %}
								<label class="w-50 p-1">
									{{ radio(class="card-radio d-none") }}
									<div class="card rounded-0 h-100">
										<div class="card-body p-3">
											<span class="card-title">
												<div class="form-check">
													<input class="form-check-input card-radio-clone" type="radio" style="visibility: hidden;" checked>
													<span class="form-check-label text-center">
														<h5>{{ radio.label }}</h5>
													</span>
													{% if radio.data == "teams" %}
													<ul class="p-0 small">
														<li>{% trans %}Participants register accounts and form teams{% endtrans %}</li>
														<li>{% trans %}If a team member solves a challenge, the entire team receives credit{% endtrans %}</li>
														<br>
														<li>{% trans %}Easier to see which team member solved a challenge{% endtrans %}</li>
														<li>{% trans %}May be slightly more difficult to administer{% endtrans %}</li>
													</ul>
													{% elif radio.data == "users" %}
													<ul class="p-0 small">
														<li>{% trans %}Participants only register an individual account{% endtrans %}</li>
														<li>{% trans %}Players can share accounts to form pseudo-teams{% endtrans %}</li>
														<br>
														<li>{% trans %}Easier to organize{% endtrans %}</li>
														<li>{% trans %}Difficult to attribute solutions to individual team members{% endtrans %}</li>
													</ul>
													{% endif %}
												</div>
											</span>
										</div>
									</div>
								</label>
							{% endfor %}
							</div>
						</div>

						<div class="text-right float-end">
							<button type="button" class="btn btn-primary btn-outlined tab-next" data-href="#settings" @click="switchTab">
								{% trans %}Next{% endtrans %}
							</button>
						</div>
					</div>
					<div class="tab-pane fade" id="settings" role="tabpanel">
						<b>{% trans %}Visibility Settings{% endtrans %}</b>
						<br>
						<small class="form-text text-muted">
							{% trans %}Control the visibility of different sections of CTFd{% endtrans %}
						</small>
						<div class="row pt-3">
							<div class="col-md-3 mb-3">
								<b>
									{{ form.challenge_visibility.label }}
									<i class="far fa-question float-end text-muted" data-bs-toggle="tooltip" data-bs-title="{{ form.challenge_visibility.description }}"></i>
								</b>
								{{ form.challenge_visibility(class="form-control custom-select") }}
							</div>

							<div class="col-md-3 mb-3">
								<b>
									{{ form.account_visibility.label }}
									<i class="far fa-question float-end text-muted" data-bs-toggle="tooltip" data-bs-title="{{ form.account_visibility.description }}"></i>
								</b>
								{{ form.account_visibility(class="form-control custom-select") }}
							</div>

							<div class="col-md-3 mb-3">
								<b>
									{{ form.score_visibility.label }}
									<i class="far fa-question float-end text-muted" data-bs-toggle="tooltip" data-bs-title="{{ form.score_visibility.description }}"></i>
								</b>
								{{ form.score_visibility(class="form-control custom-select") }}
							</div>

							<div class="col-md-3 mb-3">
								<b>
									{{ form.registration_visibility.label }}
									<i class="far fa-question float-end text-muted" data-bs-toggle="tooltip" data-bs-title="{{ form.registration_visibility.description }}"></i>
								</b>
								{{ form.registration_visibility(class="form-control custom-select") }}
							</div>
						</div>

						<hr>

						<div class="mb-3">
							<b>{{ form.verify_emails.label }}</b>
							{{ form.verify_emails(class="form-control custom-select") }}
							<small class="form-text text-muted">
								{{ form.verify_emails.description }}
							</small>
						</div>

						<div class="mb-3">
							<b>{{ form.team_size.label }}</b>
							{{ form.team_size(class="form-control custom-select") }}
							<small class="form-text text-muted">
								{{ form.team_size.description }}
							</small>
						</div>

						<div class="text-right float-end">
							<button type="button" class="btn btn-primary btn-outlined tab-next" data-href="#administration" @click="switchTab">
								{% trans %}Next{% endtrans %}
							</button>
						</div>
					</div>
					<div class="tab-pane fade" id="administration" role="tabpanel">
						<div class="mb-3">
							<b>{{ form.name.label }}</b>
							{{ form.name(class="form-control") }}
							<small class="form-text text-muted">
								{{ form.name.description }}
							</small>
						</div>
						<div class="mb-3">
							<b>{{ form.email.label }}</b>
							{{ form.email(class="form-control") }}
							<small class="form-text text-muted">
								{{ form.email.description }}
							</small>
						</div>
						<div class="mb-3">
							<b>{{ form.password.label }}</b>
							{{ form.password(class="form-control") }}
							<small class="form-text text-muted">
								{{ form.password.description }}
							</small>
						</div>

						<div class="mb-3 form-check">
							<label class="form-check-label">
								<input class="form-check-input" type="checkbox" value="" id="newsletter-checkbox" checked>
								{% trans %}Subscribe email address to the CTFd LLC Newsletter for news and updates{% endtrans %}
							</label>
						</div>

						<div class="text-right float-end">
							<button type="button" class="btn btn-primary btn-outlined tab-next" data-href="#style" @click="switchTab">
								{% trans %}Next{% endtrans %}
							</button>
						</div>
					</div>
					<div class="tab-pane fade" id="style" role="tabpanel">
						<div class="mb-3">
							<b>{{ form.ctf_logo.label }}</b>
							<div class="row">
								{{ form.ctf_logo(class="form-control-file", accept="image/*", **{'@change': 'validateFileSize($event, 128000)'}) }}
								<small class="form-text text-muted">
									{{ form.ctf_logo.description }}
								</small>
							</div>
						</div>
						<div class="mb-3">
							<b>{{ form.ctf_banner.label }}</b>
							<div class="row">
								{{ form.ctf_banner(class="form-control-file", accept="image/*", **{'@change': 'validateFileSize($event, 512000)'}) }}
								<small class="form-text text-muted">
									{{ form.ctf_banner.description }}
								</small>
							</div>
						</div>
						<div class="mb-3">
							<b>{{ form.ctf_small_icon.label }}</b>
							<div class="row">
								{{ form.ctf_small_icon(class="form-control-file", accept=".png", **{'@change': 'validateFileSize($event, 32000)'}) }}
								<small class="form-text text-muted">
									{{ form.ctf_small_icon.description }}
								</small>
							</div>
						</div>
						<div class="mb-3">
							<b>{{ form.ctf_theme.label }}</b>
							{{ form.ctf_theme(class="form-control custom-select") }}
							<small class="form-text text-muted">
								{{ form.ctf_theme.description }}
							</small>
						</div>
						<div class="mb-3">
							<b>{{ form.theme_color.label }}</b>
							<div class="row">
								<div class="d-inline-block">
									{{ form.theme_color(id="config-color-input") }}
									<div class="btn-group">
										<input type="color" id="config-color-picker" class="pr-1" style="width: 100px; height: 30px;" value="" @input="setThemeColor">
									</div>
									<div class="btn-group">
										<button type="button" id="config-color-reset" @click="resetThemeColor">Reset</button>
									</div>
								</div>
								<small class="form-text text-muted">
									{{ form.theme_color.description }}
								</small>
							</div>
						</div>
						<div class="text-right float-end">
							<button type="button" class="btn btn-primary btn-outlined tab-next" data-href="#datetime" @click="switchTab">
								{% trans %}Next{% endtrans %}
							</button>
						</div>
					</div>
					<div class="tab-pane fade" id="datetime" role="tabpanel">
						<div class="mb-3">
							<b>{{ form.start.label }}</b>
							<div class="row">
								<div class="col-md-4">
									<label>{% trans %}Date{% endtrans %}</label>
									<input class="form-control" id="start-date" type="date" placeholder="yyyy-mm-dd" data-preview="#start" @click="processDateTime('start')"/>
								</div>
								<div class="col-md-4">
									<label>{% trans %}Time{% endtrans %}</label>
									<input class="form-control" id="start-time" type="time" placeholder="hh:mm" data-preview="#start" @click="processDateTime('start')"/>
								</div>
								<div class="col-md-4">
									<label>UTC Preview</label>
									{{ form.start(class="form-control", id="start-preview", readonly=True) }}
								</div>
							</div>
							<small class="form-text text-muted">
								{{ form.start.description }}
							</small>
						</div>

						<div class="mb-3">
							<b>{{ form.end.label }}</b>
							<div class="row">
								<div class="col-md-4">
									<label>{% trans %}Date{% endtrans %}</label>
									<input class="form-control" id="end-date" type="date" placeholder="yyyy-mm-dd" data-preview="#end" @click="processDateTime('end')"/>
								</div>
								<div class="col-md-4">
									<label>{% trans %}Time{% endtrans %}</label>
									<input class="form-control" id="end-time" type="time" placeholder="hh:mm" data-preview="#end" @click="processDateTime('end')"/>
								</div>
								<div class="col-md-4">
									<label>{% trans %}UTC Preview{% endtrans %}</label>
									{{ form.end(class="form-control", id="end-preview", readonly=True) }}
								</div>
							</div>
							<small class="form-text text-muted">
								{{ form.end.description }}
							</small>
						</div>

						<div class="text-right float-end">
							<button type="button" class="btn btn-primary btn-outlined tab-next" data-href="#integrations" @click="switchTab">
								{% trans %}Next{% endtrans %}
							</button>
						</div>
					</div>
					<div class="tab-pane fade" id="integrations" role="tabpanel">
						<div class="mb-3">
							<b>{{ form.social_shares.label }}</b>
							{{ form.social_shares(class="form-control custom-select") }}
							<small class="form-text text-muted">
								{{ form.social_shares.description }}
							</small>
						</div>
						<div class="mb-3">
							<b>MajorLeagueCyber Integration</b>
							<p>MajorLeagueCyber (MLC) is a cyber security event tracker written and maintained by the developers of CTFd.</p>
							<button type="button" id="integration-mlc" class="btn btn-primary btn-block" @click="mlcSetup()">
								Setup
							</button>
						</div>

						<br>
						<hr>
						<br>

						<div class="submit-row text-right float-end">
							{{ form.submit(class="btn btn-md btn-primary btn-outlined") }}
						</div>
					</div>
				</div>

				{{ form.nonce() }}
			</form>
			{% endwith %}
		</div>
	</div>

{% endblock %}

{% block scripts %}
<script>
	var STATE = {{ state | tojson }};
</script>
{{ Assets.js("assets/js/setup.js") }}
{% endblock %}

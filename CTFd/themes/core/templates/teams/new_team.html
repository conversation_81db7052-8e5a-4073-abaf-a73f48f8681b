{% extends "base.html" %}

{% block content %}
  <div class="jumbotron">
    <div class="container">
      <h1>{% trans %}Create Team{% endtrans %}</h1>
    </div>
  </div>
  <div class="container">
    <div class="row">
      <div class="col-md-6 offset-md-3">
        {% include "components/errors.html" %}

        {% with form = Forms.teams.TeamRegisterForm() %}
          {% from "macros/forms.html" import render_extra_fields %}
          <form method="POST">
            <div class="mb-3">
              <b>{{ form.name.label(class="form-label") }}</b>
              {{ form.name(class="form-control") }}
            </div>
            <div class="mb-3">
              <b>{{ form.password.label(class="form-label") }}</b>
              {{ form.password(class="form-control") }}
            </div>

            {{ render_extra_fields(form.extra) }}

            <div class="row ">
              <div class="col-md-12">
                <p>After creating your team, share the team name and password with your teammates so they can join your
                  team.</p>
                {{ form.submit(class="btn btn-success float-end px-4") }}
              </div>
            </div>
            {{ form.nonce() }}
          </form>
        {% endwith %}
      </div>
    </div>
  </div>
{% endblock %}

{% extends "base.html" %}

{% block content %}
  <div class="jumbotron">
    <div class="container">
      <h1>{{ user.name }}</h1>

      {% if user.team_id %}
        <h2>
          <a href="{{ url_for('teams.public', team_id=user.team_id) }}">
						<span class="badge bg-secondary">
							{{ user.team.name }}
						</span>
          </a>
        </h2>
      {% endif %}

      <div class="pt-2">
        {% if user.oauth_id %}
          <a href="https://majorleaguecyber.org/u/{{ user.name }}">
            <h3 class="d-inline-block mx-1"><span class="badge rounded-pill bg-primary">{% trans %}Official{% endtrans %}</span></h3>
          </a>
        {% endif %}

        {% if user.affiliation %}
          <h3 class="d-inline-block mx-1">
            <span class="badge rounded-pill bg-primary">{{ user.affiliation }}</span>
          </h3>
        {% endif %}

        {% if user.country %}
          <h3 class="d-inline-block mx-1">
            <span class="badge rounded-pill bg-primary">
                <i class="flag-{{ user.country.lower() }}"></i>
                {{ lookup_country_code(user.country) }}
            </span>
          </h3>
        {% endif %}
      </div>

      {% if user.bracket_id %}
        <div class="pt-2">
          <h2>
            <span class="badge text-bg-light">
              {{ user.bracket.name }}
            </span>
          </h2>
        </div>
      {% endif %}

      <div class="pt-2">
        {% for field in user.fields %}
          <h3 class="d-block">
            {{ field.name }}: {{ field.value }}
          </h3>
        {% endfor %}
      </div>

      {% if user.fields %}
        <hr class="w-50 mx-auto">
      {% endif %}

      <div>
        <h2 class="text-center">
          {% if account.place %}
            {{ account.place }} <small>place</small>
          {% endif %}
        </h2>
        <h2 class="text-center">
          {% if account %}
            {{ account.get_score(admin=True) }} <small>points</small>
          {% endif %}
        </h2>
      </div>

      <div class="pt-3">
        {% if user.website %}
          <a href="{{ user.website }}" target="_blank" style="color: inherit;" rel="noopener">
            <i
                class="fas fa-external-link-alt fa-2x px-2" data-toggle="tooltip" data-placement="top"
                title="{{ user.website }}"
            ></i>
          </a>
        {% endif %}
      </div>
    </div>
  </div>
  <div class="container">
    {% include "components/errors.html" %}

    {% set solves = user.solves %}
    {% set awards = user.awards %}

    {% if solves or awards %}
      {% if awards %}
        <div class="row">
          <div class="col-md-12">
            <h3>{% trans %}Awards{% endtrans %}</h3>

          </div>
          {% for award in awards %}
            <div class="col-md-3 col-sm-6">
              <p class="text-center">
                <i class="award-icon award-{{ award.icon }} fa-2x"></i>
                <br>
                <strong>{{ award.name }}</strong>
              </p>
              <p class="text-center">{{ award.category or "" }}</p>
              <p class="text-center">{{ award.description or "" }}</p>
              <p class="text-center">{{ award.value }}</p>
            </div>
          {% endfor %}
        </div>
      {% endif %}

      <br>

      <div class="row">
        <div class="col-md-12">
          <h3>{% trans %}Solves{% endtrans %}</h3>
          <table class="table table-striped align-middle">
            <thead>
            <tr>
              <th>{% trans %}Challenge{% endtrans %}</th>
              <th class="d-none d-md-block d-lg-block">{% trans %}Category{% endtrans %}</th>
              <th>{% trans %}Value{% endtrans %}</th>
              <th>{% trans %}Time{% endtrans %}</th>
            </tr>
            </thead>
            <tbody>
            {% for solve in solves %}
              <tr>
                <td>
                  <a href="{{ url_for('challenges.listing') }}#{{ solve.challenge.name }}-{{ solve.challenge.id }}">
                    {{ solve.challenge.name }}
                  </a>
                </td>
                <td class="d-none d-md-block d-lg-block">{{ solve.challenge.category }}</td>
                <td>{{ solve.challenge.value }}</td>
                <td class="solve-time">
                  <span data-time="{{ solve.date | isoformat }}"></span>
                </td>
              </tr>
            {% endfor %}
            </tbody>
          </table>
        </div>
      </div>

      <div class="clearfix"></div>

      <div class="row" x-data="UserGraphs">
        <div class="col-md-6 d-none d-md-block d-lg-block py-4">
          <div class="progress">
            <div 
              class="progress-bar" 
              role="progressbar" 
              :style="{ width: `${getSolvePercentage()}%`, 'background-color': 'rgb(0, 209, 64)' }" 
            >
            </div>
            <div 
              class="progress-bar" 
              role="progressbar" 
              :style="{ width: `${getFailPercentage()}%`, 'background-color': 'rgb(207, 38, 0)' }"
            >
            </div>
          </div>
          <div class="ps-2 float-start">
            <svg height="16" width="16">
              <circle cx="8" cy="8" r="5" fill="rgb(0, 209, 64)" />
            </svg>
            <small x-text="`Solves (${getSolvePercentage()}%)`"></small>
          </div>
          <div class="ps-2 float-start">
            <svg height="16" width="16">
              <circle cx="8" cy="8" r="5" fill="rgb(207, 38, 0)" />
            </svg>
            <small x-text="`Fails (${getFailPercentage()}%)`"></small>
          </div>
        </div>
        <div class="col-md-6 d-none d-md-block d-lg-block py-4">
          <div class="progress">
            <template x-for="category in getCategoryBreakdown()" :key="category.name">
              <div 
                class="progress-bar" 
                role="progressbar" 
                :style="{ width: `${category.percent}%`, 'background-color': category.color }"
              >
              </div>
            </template>
          </div>
          <template x-for="category in getCategoryBreakdown()" :key="category.name">
            <div class="ps-2 float-start">
              <svg height="16" width="16">
                <circle cx="8" cy="8" r="5" :fill="category.color" />
              </svg>
              <small x-text="`${category.name} (${category.percent}%)`"></small>
            </div>
          </template>
        </div>
        <br class="clearfix">
        <div class="col-md-12 d-none d-md-block d-lg-block py-4">
          <div id="score-graph" x-ref="scoregraph" class="w-100 d-flex align-items-center">
            <div class="text-center w-100">
              <i class="fas fa-circle-notch fa-spin fa-3x fa-fw spinner"></i>
            </div>
          </div>
        </div>
      </div>
    {% else %}
      <div class="row min-vh-25">
        <h3 class="opacity-50 text-center w-100 justify-content-center align-self-center">
          {% trans %}No solves yet{% endtrans %}
        </h3>
      </div>
    {% endif %}
  </div>
{% endblock %}

{% block scripts %}
  {{ Assets.js("assets/js/users/private.js") }}
{% endblock %}


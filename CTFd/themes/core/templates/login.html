{% extends "base.html" %}

{% block content %}
  <div class="jumbotron">
    <div class="container">
      <h1>
        {% trans %}Login{% endtrans %}
      </h1>
    </div>
  </div>

  <div class="container">
    <div class="row">
      <div class="col-md-8 col offset-md-2 col-lg-6 offset-lg-3">
        {% include "components/errors.html" %}

        {% if integrations.mlc() %}
          <a class="btn btn-secondary btn-lg btn-block w-100" href="{{ url_for('auth.oauth_login') }}">
            Login with Major League Cyber
          </a>

          <hr>
        {% endif %}

        {% with form = Forms.auth.LoginForm() %}
          <form method="post" accept-charset="utf-8">
            <div class="mb-3">
              <b>{{ form.name.label(class="form-label") }}</b>
              {{ form.name(class="form-control", value=name, autocomplete="username email") }}
            </div>

            <div class="mb-3">
              <b>{{ form.password.label(class="form-label") }}</b>
              {{ form.password(class="form-control", value=password, autocomplete="current-password") }}
            </div>

            <div class="row pt-3">
              <div class="col-6 col-md-8">
                <a class="align-middle" href="{{ url_for('auth.reset_password') }}">
                  {% trans %}Forgot your password?{% endtrans %}
                </a>
              </div>

              <div class="col-6 col-md-4">
                {{ form.submit(class="btn btn-block btn-primary w-100") }}
              </div>
            </div>

            {{ form.nonce() }}
          </form>
        {% endwith %}
      </div>
    </div>
  </div>
{% endblock %}

{% extends "base.html" %}

{% block content %}
  <div class="jumbotron">
    <div class="container">
      <h1>
        {% trans %}Confirm{% endtrans %}
      </h1>
    </div>
  </div>

  <div class="container">
    <div class="row">
      <div class="col-md-10 col-lg-8 col-xl-6 offset-md-1 offset-lg-2 offset-xl-3">
        {% include "components/errors.html" %}

        {% if request.args.get('flow') == "init" %}
          <h5 class="text-center">
            {% trans %}To send a confirmation email to your email address, please click the button below.{% endtrans %}
          </h5>
          <br>
          <h5 class="text-center">
            {% trans %}Please click the link in that email to confirm your account.{% endtrans %}
          </h5>
          <h5 class="text-center">
            {% trans %}If the email doesn’t arrive, check your spam folder or contact an administrator to manually verify your account.{% endtrans %}
          </h5>
        {% else%}
          <h5 class="text-center">
            {% trans %}We've sent a confirmation email to your email address.{% endtrans %}
          </h5>
          <br>
          <h5 class="text-center">
            {% trans %}Please click the link in that email to confirm your account.{% endtrans %}
          </h5>
          <h5 class="text-center">
            {% trans %}If the email doesn’t arrive, check your spam folder or contact an administrator to manually verify your account.{% endtrans %}
          </h5>
        {% endif %}

        <hr>

        {% with form = Forms.auth.ConfirmForm() %}
          <form method="POST" action="{{ url_for('auth.confirm') }}">
            <div class="row">
              <div class="mb-3 col-md-6">
                {{ form.submit(class="btn btn-primary w-100 p-2") }}
              </div>
              <div class="mb-3 col-md-6">
                <a href="{{ url_for('views.settings') }}" class="btn btn-secondary w-100 p-2">
                  {% trans %}Change Email Address{% endtrans %}
                </a>
              </div>
              {{ form.nonce() }}
            </div>
          </form>
        {% endwith %}
      </div>
    </div>
  </div>
{% endblock %}

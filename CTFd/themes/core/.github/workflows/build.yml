name: build

on:
  push:
    branches: main

jobs:
  static:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
        with:
          repository: ${{ github.event.pull_request.head.repo.full_name }}
          ref: ${{ github.event.pull_request.head.ref }}
      - name: Setup node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16.x'
      - name: Install dependencies
        run: yarn --frozen-lockfile
      - name: Format
        run: yarn format
      - name: Build
        run: yarn build
      - name: Push built files
        uses: EndBug/add-and-commit@v9
        with:
          default_author: github_actions
          message: "chore: yarn build"
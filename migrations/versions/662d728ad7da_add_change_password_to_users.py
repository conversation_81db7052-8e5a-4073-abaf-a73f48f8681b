"""Add change_password to Users

Revision ID: 662d728ad7da
Revises: f73a96c97449
Create Date: 2025-08-11 16:38:48.891702

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "662d728ad7da"
down_revision = "f73a96c97449"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("users", sa.<PERSON>umn("change_password", sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("users", "change_password")
    # ### end Alembic commands ###

"""Add target column to Tracking

Revision ID: 55623b100da8
Revises: 5c98d9253f56
Create Date: 2025-08-26 23:30:29.170546

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "55623b100da8"
down_revision = "5c98d9253f56"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("tracking", sa.Column("target", sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("tracking", "target")
    # ### end Alembic commands ###

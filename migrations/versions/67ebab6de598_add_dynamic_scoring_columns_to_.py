"""Add dynamic scoring columns to Challenges table

Revision ID: 67ebab6de598
Revises: 24ad6790bc3c
Create Date: 2025-09-11 08:57:33.156731

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "67ebab6de598"
down_revision = "24ad6790bc3c"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("challenges", sa.<PERSON>umn("initial", sa.Integer(), nullable=True))
    op.add_column("challenges", sa.Column("minimum", sa.Integer(), nullable=True))
    op.add_column("challenges", sa.Column("decay", sa.Integer(), nullable=True))
    op.add_column(
        "challenges", sa.Column("function", sa.String(length=32), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("challenges", "function")
    op.drop_column("challenges", "decay")
    op.drop_column("challenges", "minimum")
    op.drop_column("challenges", "initial")
    # ### end Alembic commands ###

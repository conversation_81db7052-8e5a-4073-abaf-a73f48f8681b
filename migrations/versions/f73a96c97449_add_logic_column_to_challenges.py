"""Add logic column to Challenges

Revision ID: f73a96c97449
Revises: 62bf576b2cd3
Create Date: 2025-08-08 21:49:23.417694

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "f73a96c97449"
down_revision = "62bf576b2cd3"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "challenges", sa.Column("logic", sa.String(length=80), nullable=False)
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("challenges", "logic")
    # ### end Alembic commands ###

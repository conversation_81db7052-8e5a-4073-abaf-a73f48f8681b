"""Add ratings table

Revision ID: 364b4efa1686
Revises: 662d728ad7da
Create Date: 2025-08-17 07:07:41.484323

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "364b4efa1686"
down_revision = "662d728ad7da"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "ratings",
        sa.<PERSON>umn("id", sa.Integer(), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=True),
        sa.Column("challenge_id", sa.Integer(), nullable=True),
        sa.<PERSON>umn("value", sa.Integer(), nullable=True),
        sa.Column("review", sa.String(length=2000), nullable=True),
        sa.Column("date", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["challenge_id"], ["challenges.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("user_id", "challenge_id"),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("ratings")
    # ### end Alembic commands ###

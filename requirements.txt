#
# This file is autogenerated by pip-compile with Python 3.11
# by the following command:
#
#    ./scripts/pip-compile.sh
#
alembic==1.16.4
    # via
    #   dataset
    #   flask-migrate
aniso8601==10.0.1
    # via flask-restx
attrs==25.3.0
    # via
    #   jsonschema
    #   referencing
babel==2.17.0
    # via flask-babel
banal==1.0.6
    # via dataset
bcrypt==4.0.1
    # via -r requirements.in
boto3==1.35.27
    # via -r requirements.in
botocore==1.35.99
    # via
    #   boto3
    #   s3transfer
cachelib==0.9.0
    # via flask-caching
certifi==2025.8.3
    # via requests
cffi==1.17.1
    # via
    #   -r requirements.in
    #   cmarkgfm
    #   cryptography
charset-normalizer==3.4.3
    # via requests
click==8.2.1
    # via flask
cmarkgfm==2024.11.20
    # via -r requirements.in
cryptography==45.0.6
    # via pymysql
dataset==1.6.2
    # via -r requirements.in
flask==2.1.3
    # via
    #   -r requirements.in
    #   flask-babel
    #   flask-caching
    #   flask-marshmallow
    #   flask-migrate
    #   flask-restx
    #   flask-script
    #   flask-sqlalchemy
flask-babel==2.0.0
    # via -r requirements.in
flask-caching==2.3.1
    # via -r requirements.in
flask-marshmallow==0.10.1
    # via -r requirements.in
flask-migrate==2.5.3
    # via -r requirements.in
flask-restx==1.3.0
    # via -r requirements.in
flask-script==2.0.6
    # via -r requirements.in
flask-sqlalchemy==2.5.1
    # via
    #   -r requirements.in
    #   flask-migrate
freezegun==1.5.5
    # via -r requirements.in
gevent==25.5.1
    # via -r requirements.in
greenlet==3.2.4
    # via
    #   gevent
    #   sqlalchemy
gunicorn==23.0.0
    # via -r requirements.in
idna==3.10
    # via requests
importlib-resources==6.5.2
    # via flask-restx
itsdangerous==2.2.0
    # via flask
jinja2==3.1.6
    # via
    #   -r requirements.in
    #   flask
    #   flask-babel
jmespath==1.0.1
    # via
    #   boto3
    #   botocore
jsonschema==4.25.0
    # via flask-restx
jsonschema-specifications==2025.4.1
    # via jsonschema
mako==1.3.10
    # via alembic
markupsafe==3.0.2
    # via
    #   jinja2
    #   mako
    #   werkzeug
    #   wtforms
marshmallow==2.20.2
    # via
    #   -r requirements.in
    #   flask-marshmallow
    #   marshmallow-sqlalchemy
marshmallow-sqlalchemy==0.17.0
    # via -r requirements.in
maxminddb==1.5.4
    # via
    #   -r requirements.in
    #   python-geoacumen-city
nh3==0.3.0
    # via -r requirements.in
packaging==25.0
    # via gunicorn
passlib==1.7.4
    # via -r requirements.in
pillow==11.3.0
    # via -r requirements.in
pycparser==2.22
    # via cffi
pydantic==1.6.2
    # via -r requirements.in
pymysql[rsa]==1.1.1
    # via
    #   -r requirements.in
    #   pymysql
python-dateutil==2.9.0.post0
    # via
    #   botocore
    #   freezegun
python-dotenv==0.13.0
    # via -r requirements.in
python-geoacumen-city==2023.4.15
    # via -r requirements.in
pytz==2025.2
    # via
    #   flask-babel
    #   flask-restx
redis==4.5.5
    # via -r requirements.in
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
requests==2.32.4
    # via -r requirements.in
rpds-py==0.27.0
    # via
    #   jsonschema
    #   referencing
s3transfer==0.10.4
    # via boto3
six==1.17.0
    # via
    #   -r requirements.in
    #   flask-marshmallow
    #   python-dateutil
sqlalchemy==1.4.54
    # via
    #   -r requirements.in
    #   alembic
    #   dataset
    #   flask-sqlalchemy
    #   marshmallow-sqlalchemy
    #   sqlalchemy-utils
sqlalchemy-utils==0.41.1
    # via -r requirements.in
tenacity==9.1.2
    # via -r requirements.in
typing-extensions==4.14.1
    # via
    #   alembic
    #   referencing
urllib3==2.5.0
    # via
    #   -r requirements.in
    #   botocore
    #   requests
werkzeug==2.2.3
    # via
    #   -r requirements.in
    #   flask
    #   flask-restx
wtforms==2.3.1
    # via -r requirements.in
zope-event==5.1.1
    # via gevent
zope-interface==7.2
    # via gevent

# The following packages are considered to be unsafe in a requirements file:
# setuptools
